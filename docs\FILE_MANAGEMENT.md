# Support Ticket File Management

## Overview
The support ticket system provides smart file management for both ticket creation and updates. Files can be added, removed, or replaced during ticket updates.

## API Endpoints

### Create Ticket
- **Endpoint**: `POST /v1/private/tickets/create`
- **File Field**: `ticketFiles` (up to 5 files, max 50MB each)
- **Behavior**: All uploaded files are attached to the new ticket

### Update Ticket
- **Endpoint**: `PUT /v1/private/tickets/update/:id`
- **File Field**: `ticketFiles` (up to 5 files, max 50MB each)
- **Additional Parameters**:
  - `attachment_action`: Controls how files are managed
  - `remove_attachment_ids`: Specifies which existing files to remove

## File Management Parameters

### `attachment_action` (optional)
Controls the file management strategy:
- `smart` (default): Only process if there are actual changes
- `add`: Add new files to existing ones
- `replace`: Remove all existing files and add new ones
- `remove`: Only remove specified files (no new uploads)

### `remove_attachment_ids` (optional)
Specifies which existing attachments to remove. Accepts multiple formats:
- **Array**: `[1, 2, 3]`
- **JSON String**: `"[1,2,3]"`
- **Comma-separated String**: `"1,2,3"`

## Usage Examples

### Example 1: Create Ticket with Files
```javascript
const formData = new FormData();
formData.append('ticket_title', 'Test Issue');
formData.append('ticket_description', 'Description here');
formData.append('ticketFiles', file1); // test.png
formData.append('ticketFiles', file2); // document.pdf

// Result: Ticket created with 2 files
```

### Example 2: Add More Files to Existing Ticket
```javascript
const formData = new FormData();
formData.append('attachment_action', 'add');
formData.append('ticketFiles', newFile); // test1.png

// Result: Original files + new file (total: 3 files)
```

### Example 3: Remove Specific Files
```javascript
const formData = new FormData();
formData.append('remove_attachment_ids', '[1,2]'); // Remove files with IDs 1 and 2

// Result: Files with IDs 1 and 2 are removed
```

### Example 4: Remove and Add Files in One Update
```javascript
const formData = new FormData();
formData.append('attachment_action', 'smart');
formData.append('remove_attachment_ids', '1'); // Remove file with ID 1
formData.append('ticketFiles', newFile); // Add new file

// Result: File ID 1 removed, new file added
```

### Example 5: Replace All Files
```javascript
const formData = new FormData();
formData.append('attachment_action', 'replace');
formData.append('ticketFiles', newFile1);
formData.append('ticketFiles', newFile2);

// Result: All existing files removed, only new files remain
```

## Response Format

The API returns attachment information in this format:
```json
{
  "status": true,
  "message": "Ticket updated successfully",
  "data": {
    "id": 57,
    "ticket_title": "Test Issue",
    "attachments": [
      {
        "id": 39,
        "ticket_id": 57,
        "attachment_name": "circle-line-simple-design-logo-600w-2174926571.webp",
        "file_size": 19206,
        "mime_type": "image/webp",
        "attachment_type": "image"
      }
    ]
  }
}
```

## Frontend Implementation Tips

1. **Show Existing Files**: Display current attachments with remove buttons
2. **File Selection**: Allow users to select new files to upload
3. **Remove Files**: Collect IDs of files to remove and send in `remove_attachment_ids`
4. **Smart Updates**: Use `attachment_action: 'smart'` for most cases
5. **Error Handling**: Handle validation errors for file size/type limits

## File Constraints

- **Maximum Files**: 5 files per ticket
- **Maximum Size**: 50MB per file
- **Allowed Types**: Images, documents, archives (configurable per organization)
- **Storage**: Files are stored in AWS S3 with references in the database
