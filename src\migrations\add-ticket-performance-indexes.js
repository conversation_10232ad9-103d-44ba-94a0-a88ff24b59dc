'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add indexes for better query performance on mo_support_tickets table
      
      // Index for organization_id (frequently used in WHERE clauses)
      await queryInterface.addIndex('mo_support_tickets', ['organization_id'], {
        name: 'idx_tickets_organization_id',
        concurrently: true
      });

      // Index for ticket_status (frequently filtered)
      await queryInterface.addIndex('mo_support_tickets', ['ticket_status'], {
        name: 'idx_tickets_status',
        concurrently: true
      });

      // Index for ticket_priority (frequently filtered)
      await queryInterface.addIndex('mo_support_tickets', ['ticket_priority'], {
        name: 'idx_tickets_priority',
        concurrently: true
      });

      // Index for assigned_to_user_id (frequently used for agent queries)
      await queryInterface.addIndex('mo_support_tickets', ['assigned_to_user_id'], {
        name: 'idx_tickets_assigned_to',
        concurrently: true
      });

      // Index for ticket_owner_user_id (frequently used for user's own tickets)
      await queryInterface.addIndex('mo_support_tickets', ['ticket_owner_user_id'], {
        name: 'idx_tickets_owner',
        concurrently: true
      });

      // Index for created_at (frequently used for sorting and date filtering)
      await queryInterface.addIndex('mo_support_tickets', ['created_at'], {
        name: 'idx_tickets_created_at',
        concurrently: true
      });

      // Index for updated_at (frequently used for sorting)
      await queryInterface.addIndex('mo_support_tickets', ['updated_at'], {
        name: 'idx_tickets_updated_at',
        concurrently: true
      });

      // Index for sla_due_date (used for overdue ticket queries)
      await queryInterface.addIndex('mo_support_tickets', ['sla_due_date'], {
        name: 'idx_tickets_sla_due_date',
        concurrently: true
      });

      // Composite index for organization + status (common filter combination)
      await queryInterface.addIndex('mo_support_tickets', ['organization_id', 'ticket_status'], {
        name: 'idx_tickets_org_status',
        concurrently: true
      });

      // Composite index for organization + assigned user (common for agent dashboards)
      await queryInterface.addIndex('mo_support_tickets', ['organization_id', 'assigned_to_user_id'], {
        name: 'idx_tickets_org_assigned',
        concurrently: true
      });

      // Index for deleted_at (used in all queries to exclude deleted records)
      await queryInterface.addIndex('mo_support_tickets', ['deleted_at'], {
        name: 'idx_tickets_deleted_at',
        concurrently: true
      });

      // Index for ticket_slug (unique lookups)
      await queryInterface.addIndex('mo_support_tickets', ['ticket_slug'], {
        name: 'idx_tickets_slug',
        unique: true,
        concurrently: true
      });

      // Indexes for mo_support_ticket_messages table
      await queryInterface.addIndex('mo_support_ticket_messages', ['ticket_id'], {
        name: 'idx_ticket_messages_ticket_id',
        concurrently: true
      });

      await queryInterface.addIndex('mo_support_ticket_messages', ['created_at'], {
        name: 'idx_ticket_messages_created_at',
        concurrently: true
      });

      // Indexes for mo_support_ticket_attachments table
      await queryInterface.addIndex('mo_support_ticket_attachments', ['ticket_id'], {
        name: 'idx_ticket_attachments_ticket_id',
        concurrently: true
      });

      // Indexes for mo_support_ticket_history table
      await queryInterface.addIndex('mo_support_ticket_history', ['ticket_id'], {
        name: 'idx_ticket_history_ticket_id',
        concurrently: true
      });

      await queryInterface.addIndex('mo_support_ticket_history', ['created_at'], {
        name: 'idx_ticket_history_created_at',
        concurrently: true
      });

      console.log('✅ Performance indexes added successfully');
    } catch (error) {
      console.error('❌ Error adding performance indexes:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Remove all the indexes we added
      const indexesToRemove = [
        'idx_tickets_organization_id',
        'idx_tickets_status',
        'idx_tickets_priority',
        'idx_tickets_assigned_to',
        'idx_tickets_owner',
        'idx_tickets_created_at',
        'idx_tickets_updated_at',
        'idx_tickets_sla_due_date',
        'idx_tickets_org_status',
        'idx_tickets_org_assigned',
        'idx_tickets_deleted_at',
        'idx_tickets_slug',
        'idx_ticket_messages_ticket_id',
        'idx_ticket_messages_created_at',
        'idx_ticket_attachments_ticket_id',
        'idx_ticket_history_ticket_id',
        'idx_ticket_history_created_at'
      ];

      for (const indexName of indexesToRemove) {
        try {
          await queryInterface.removeIndex('mo_support_tickets', indexName);
        } catch (error) {
          // Index might not exist, continue with others
          console.warn(`Index ${indexName} not found, skipping...`);
        }
      }

      console.log('✅ Performance indexes removed successfully');
    } catch (error) {
      console.error('❌ Error removing performance indexes:', error);
      throw error;
    }
  }
};
