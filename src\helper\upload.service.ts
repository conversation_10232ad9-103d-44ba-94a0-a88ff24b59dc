import multer from "multer";
import path from "path";
import crypto from "crypto";
import {
  S3Client,
  PutObjectCommand,
  HeadObjectCommand,
  HeadBucketCommand,
  CreateBucketCommand,
  CopyObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { getHash, SUPPORT_FILE_UPLOAD_CONSTANT } from "./common";
import { db } from "../models";

// Import Item model and enums directly
import {
  Item,
  item_external_location,
  item_IEC,
  item_status,
  item_type,
} from "../models/Item";

// Setup S3 client (MinIO compatible)
const s3 = new S3Client({
  endpoint: global.config.MINIO_ENDPOINT,
  region: "us-east-1",
  forcePathStyle: true,
  credentials: {
    accessKeyId: global.config.MINIO_ACCESS_KEY,
    secretAccessKey: global.config.MINIO_SECRET_KEY,
  },
});

/**
 * Determine the correct folder path based on field name and context
 * @param fieldName - The form field name (e.g., 'ticketAttachment', 'messageAttachment')
 * @param fileName - The clean file name
 * @param req - The request object to get context
 * @param entityId - Optional entity ID (ticketId, messageId, etc.)
 * @returns The folder path for the file
 */
const determineFolderPath = (
  fieldName: string,
  fileName: string,
  req: any,
  entityId?: any
): string => {
  const isSystemDefault = !req.user?.organization_id;
  const orgName = isSystemDefault ? null : req.user?.organization_id;

  // Map field names to upload constants
  switch (fieldName) {
    case "ticketAttachment":
    case "ticketAttachments":
    case "ticketFiles":
      return SUPPORT_FILE_UPLOAD_CONSTANT.TICKET_ATTACHMENT.destinationPath(
        orgName,
        entityId,
        fileName
      );
    case "messageAttachment":
    case "messageAttachments":
    case "attachment":
      return SUPPORT_FILE_UPLOAD_CONSTANT.MESSAGE_ATTACHMENT.destinationPath(
        orgName,
        entityId,
        null,
        fileName
      );
    default: {
      // Fallback for unknown field names
      const fallbackPath = isSystemDefault
        ? "support_defaults/support_misc"
        : `${orgName}/support_misc`;
      return `${fallbackPath}/${fileName}`;
    }
  }
};

/**
 * Auto-detect field name based on file MIME type for support uploads
 * @param mimeType - The file MIME type
 * @param originalFieldName - The original field name from the form
 * @returns Enhanced field name for proper routing
 */
const enhanceFieldNameByMimeType = (
  mimeType: string,
  originalFieldName: string
): string => {
  // If field name already specifies the type, use it as-is
  if (
    originalFieldName.includes("Attachment") ||
    originalFieldName.includes("File")
  ) {
    return originalFieldName;
  }

  // Return original field name if no enhancement needed
  return originalFieldName;
};

/**
 * S3 upload service with hash verification to prevent duplicates
 * @param bucketName - The S3 bucket name
 * @param folderPath - Optional folder path within the bucket
 * @returns multer instance configured for S3 storage
 */
const multerS3 = (bucketName: string, folderPath: string = "") => {
  try {
    // Use memory storage for direct S3 upload (no local files)
    const storage = multer.memoryStorage();
    const upload = multer({
      storage,
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit for support files
      },
      fileFilter: (req: any, file: any, cb: any) => {
        try {
          // Validate file object
          if (!file || !file.mimetype || !file.originalname) {
            return cb(new Error("Invalid file: missing required properties"));
          }

          // Check for potentially dangerous file names
          const dangerousPatterns = [
            /\.\./,  // Directory traversal
            /[<>:"|?*]/,  // Invalid filename characters
            /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Windows reserved names
          ];

          if (dangerousPatterns.some(pattern => pattern.test(file.originalname))) {
            return cb(new Error(`Filename "${file.originalname}" contains invalid characters or patterns`));
          }

          // Enhanced file type validation
          const allowedMimes = [
            // Images
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/webp",
            "image/bmp",
            "image/tiff",
            "image/svg+xml",
            // Videos
            "video/mp4",
            "video/webm",
            "video/avi",
            "video/mov",
            "video/quicktime",
            "video/wmv",
            "video/mkv",
            // Audio
            "audio/mp3",
            "audio/mpeg",
            "audio/wav",
            "audio/ogg",
            "audio/aac",
            "audio/m4a",
            "audio/flac",
            // Documents
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "text/csv",
            "application/json",
            "application/xml",
            // Archives
            "application/zip",
            "application/x-rar-compressed",
            "application/x-7z-compressed",
            "application/x-tar",
            "application/gzip",
          ];

          if (!allowedMimes.includes(file.mimetype)) {
            return cb(new Error(`File type ${file.mimetype} is not allowed. Please upload: images, documents, videos, audio, or archives.`));
          }

          // Additional size validation per file type
          const maxSizes = {
            image: 10 * 1024 * 1024,    // 10MB for images
            video: 100 * 1024 * 1024,   // 100MB for videos
            audio: 50 * 1024 * 1024,    // 50MB for audio
            document: 25 * 1024 * 1024, // 25MB for documents
            archive: 50 * 1024 * 1024,  // 50MB for archives
          };

          let fileType = 'document'; // default
          if (file.mimetype.startsWith('image/')) fileType = 'image';
          else if (file.mimetype.startsWith('video/')) fileType = 'video';
          else if (file.mimetype.startsWith('audio/')) fileType = 'audio';
          else if (file.mimetype.includes('zip') || file.mimetype.includes('rar') || file.mimetype.includes('tar') || file.mimetype.includes('gzip')) fileType = 'archive';

          // Note: Actual file size check is done in multer limits and later in middleware
          // This is just for reference and future enhancement

          cb(null, true);
        } catch (error) {
          console.error('File filter error:', error);
          cb(new Error('File validation failed'));
        }
      },
    });

    createBucketIfNotExists(bucketName).then().catch();

    // Custom middleware for single field with single files
    const s3UploadMiddleware = (fieldName: string, maxCount: number = 1) => {
      const multerMiddleware = upload.array(fieldName, maxCount);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            return next(err);
          }

          if (!req.files || req.files?.length === 0) {
            return next();
          }

          const uploadedFiles: any[] = [];

          // Fetch organization-level support configuration once per request
          let orgConfig: any = null;
          try {
            if (req.user?.organization_id) {
              orgConfig = await db.SupportConfig.findOne({
                where: { organization_id: req.user.organization_id },
              });
            }
          } catch (cfgErr) {
            console.error(
              "Error loading support config in upload service:",
              cfgErr
            );
          }

          // If attachments are disabled via config
          if (orgConfig && orgConfig.allow_attachments === false) {
            return next(
              new Error(
                res.__("ATTACHMENTS_DISABLED") ||
                  "File attachments are disabled for this organization"
              )
            );
          }

          try {
            for (const file of req.files) {
              // Validate file buffer exists
              if (!file.buffer || file.buffer.length === 0) {
                throw new Error(`File ${file.originalname} has no content`);
              }

              // Generate file hash to check for duplicates
              const fileHash = crypto
                .createHash("md5")
                .update(file.buffer)
                .digest("hex");

              // Use clean filename without timestamp - more restrictive cleaning
              const cleanFileName = file.originalname
                .replace(/[^a-zA-Z0-9.-]/g, "_")
                .replace(/_{2,}/g, "_")  // Replace multiple underscores with single
                .replace(/^_+|_+$/g, ""); // Remove leading/trailing underscores

              const fileName = cleanFileName || `file_${Date.now()}`; // Fallback if name becomes empty

              // Enhance field name based on MIME type for better routing
              const enhancedFieldName = enhanceFieldNameByMimeType(
                file.mimetype,
                fieldName
              );

              // Determine the correct file path using the helper function
              const filePath = determineFolderPath(
                enhancedFieldName,
                fileName,
                req,
                req.params.ticketId || req.params.id
              );

              const fileBuffer = file.buffer;

              // Per-type size validation
              const IMAGE_MAX = 10 * 1024 * 1024; // 10 MB default
              const VIDEO_MAX = 200 * 1024 * 1024; // 200 MB default
              const maxSizeFromConfig = orgConfig?.max_attachment_size;

              // Use org-specific max size if provided
              const maxSize = maxSizeFromConfig || IMAGE_MAX;

              if (file.mimetype.startsWith("image/") && file.size > maxSize) {
                return next(
                  new Error(
                    res.__("IMAGE_FILE_TOO_LARGE") || "Image size exceeds limit"
                  )
                );
              }

              if (file.mimetype.startsWith("video/") && file.size > maxSize) {
                return next(
                  new Error(
                    res.__("VIDEO_FILE_TOO_LARGE") || "Video size exceeds limit"
                  )
                );
              }

              // Validate file type against organization config if provided
              if (
                orgConfig &&
                !orgConfig.isFileTypeAllowed(
                  (file.mimetype.split("/").pop() || "").toLowerCase()
                )
              ) {
                return next(
                  new Error(res.__("INVALID_FILE_TYPE") || "Invalid file type.")
                );
              }

              // Check if file already exists in the bucket
              let fileExists = false;
              let fileExistAtLocation = false;
              try {
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash,
                    item_organization_id: req.user?.organization_id,
                  },
                });
                if (getItem && getItem.id) {
                  fileExists = true;
                  const checkS3FileExist = await s3.send(
                    new GetObjectCommand({
                      Bucket: bucketName,
                      Key: getItem?.item_location,
                    })
                  );
                  if (checkS3FileExist && checkS3FileExist?.Body) {
                    fileExistAtLocation = true;
                  }
                }
              } catch (error: any) {
                console.log(error.message);
                // File doesn't exist, continue with upload
              }

              if (!fileExists) {
                // Upload file to S3
                await s3.send(
                  new PutObjectCommand({
                    Bucket: bucketName,
                    Key: filePath,
                    Body: fileBuffer,
                    ContentType: file.mimetype,
                  })
                );

                const saveItem: any = {
                  item_type:
                    file.mimetype == "multipart/form-data"
                      ? item_type.VIDEO
                      : file.mimetype == "application/octet-stream"
                        ? item_type.VIDEO
                        : file.mimetype.split("/")[0] == "application"
                          ? "pdf"
                          : file.mimetype.split("/")[0],
                  item_name: file.originalname,
                  item_hash: fileHash,
                  item_mime_type: file.mimetype,
                  item_extension: path.extname(file.originalname),
                  item_size: file.size,
                  item_IEC: item_IEC.B,
                  item_status: item_status.ACTIVE,
                  item_external_location: item_external_location.NO,
                  item_location: filePath,
                  item_organization_id: req.user?.organization_id || null,
                  created_by: req.user?.id || null,
                  updated_by: req.user?.id || null,
                };

                const item = await Item.create(saveItem);

                // Add file info to the request
                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: filePath,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash,
                  bucket: bucketName,
                  item_id: item.id,
                  type: item.item_type,
                  isMovable: true,
                });
              } else {
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash,
                    item_organization_id: req.user?.organization_id,
                  },
                });
                if (!fileExistAtLocation) {
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );
                  await Item.update(
                    {
                      item_location: filePath,
                      item_status: item_status?.ACTIVE,
                    },
                    {
                      where: {
                        id: getItem?.id,
                      },
                    }
                  );
                }

                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: filePath,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash,
                  bucket: bucketName,
                  item_id: getItem.id,
                  type: getItem.item_type,
                  isMovable: true,
                });
              }
            }

            // Set files on req object exactly like recipe microservice
            req.files = { [fieldName]: uploadedFiles };
            return next();
          } catch (error) {
            console.error("S3 upload error:", error);
            return next(error);
          }
        });
      };
    };

    // Custom middleware for multiple files
    const s3UploadArrayMiddleware = (
      fieldName: string,
      maxCount: number = 10
    ) => {
      return s3UploadMiddleware(fieldName, maxCount);
    };

    // Custom middleware for multiple fields
    const s3UploadFieldsMiddleware = (
      fields: { name: string; maxCount?: number }[]
    ) => {
      const multerMiddleware = upload.fields(fields);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            return next(err);
          }

          if (!req.files) {
            return next();
          }

          const uploadedFiles: any[] = [];

          try {
            for (const field of fields) {
              const fieldFiles = (req.files as any)[field.name] || [];

              for (const file of fieldFiles) {
                // Generate file hash to check for duplicates
                const fileHash = crypto
                  .createHash("md5")
                  .update(file.buffer)
                  .digest("hex");

                // Use clean filename
                const cleanFileName = file.originalname.replace(
                  /[^a-zA-Z0-9.-]/g,
                  "_"
                );
                const fileName = cleanFileName;

                // Enhance field name based on MIME type
                const enhancedFieldName = enhanceFieldNameByMimeType(
                  file.mimetype,
                  field.name
                );

                // Determine the correct file path
                const filePath = determineFolderPath(
                  enhancedFieldName,
                  fileName,
                  req,
                  req.params.ticketId || req.params.id
                );

                const fileBuffer = file.buffer;

                // Check if file already exists
                let fileExists = false;
                let fileExistAtLocation = false;
                try {
                  const getItem: any = await Item.findOne({
                    where: {
                      item_hash: fileHash,
                      item_organization_id: req.user?.organization_id,
                    },
                  });
                  if (getItem && getItem.id) {
                    fileExists = true;
                    const checkS3FileExist = await s3.send(
                      new GetObjectCommand({
                        Bucket: bucketName,
                        Key: getItem?.item_location,
                      })
                    );
                    if (checkS3FileExist && checkS3FileExist?.Body) {
                      fileExistAtLocation = true;
                    }
                  }
                } catch (error) {
                  // File doesn't exist, continue with upload
                }

                if (!fileExists) {
                  // Upload file to S3
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );

                  const saveItem: any = {
                    item_type:
                      file.mimetype == "multipart/form-data"
                        ? item_type.VIDEO
                        : file.mimetype == "application/octet-stream"
                          ? item_type.VIDEO
                          : file.mimetype.split("/")[0] == "application"
                            ? "pdf"
                            : file.mimetype.split("/")[0],
                    item_name: file.originalname,
                    item_hash: fileHash,
                    item_mime_type: file.mimetype,
                    item_extension: path.extname(file.originalname),
                    item_size: file.size,
                    item_IEC: item_IEC.B,
                    item_status: item_status.ACTIVE,
                    item_external_location: item_external_location.NO,
                    item_location: filePath,
                    item_organization_id: req.user?.organization_id || null,
                    created_by: req.user?.id || null,
                    updated_by: req.user?.id || null,
                  };

                  const item = await Item.create(saveItem);

                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash,
                    bucket: bucketName,
                    item_id: item.id,
                    type: item.item_type,
                    isMovable: true,
                    fieldName: field.name,
                  });
                } else {
                  const getItem: any = await Item.findOne({
                    where: {
                      item_hash: fileHash,
                      item_organization_id: req.user?.organization_id,
                    },
                  });
                  if (!fileExistAtLocation) {
                    await s3.send(
                      new PutObjectCommand({
                        Bucket: bucketName,
                        Key: filePath,
                        Body: fileBuffer,
                        ContentType: file.mimetype,
                      })
                    );
                    await Item.update(
                      {
                        item_location: filePath,
                        item_status: item_status?.ACTIVE,
                      },
                      {
                        where: {
                          id: getItem?.id,
                        },
                      }
                    );
                  }

                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem.item_type,
                    isMovable: true,
                    fieldName: field.name,
                  });
                }
              }
            }

            // Organize files by field name exactly like recipe microservice
            const processedFiles: any = {};
            for (const file of uploadedFiles) {
              if (!processedFiles[file.fieldName]) {
                processedFiles[file.fieldName] = [];
              }
              processedFiles[file.fieldName].push(file);
            }
            req.files = processedFiles;
            return next();
          } catch (error) {
            console.error("S3 fields upload error:", error);
            return next(error);
          }
        });
      };
    };

    // Single file upload middleware
    const s3SingleUploadMiddleware = (fieldName: string) => {
      return s3UploadMiddleware(fieldName, 1);
    };

    return {
      s3UploadMiddleware,
      s3UploadArrayMiddleware,
      s3UploadFieldsMiddleware,
      s3SingleUploadMiddleware,
    };
  } catch (error) {
    console.error("multerS3 configuration error:", error);

    const errorMiddleware = (_req: any, _res: any, next: any) => {
      return next(new Error("S3 upload configuration failed"));
    };

    return {
      upload: (_fieldName: string, _maxCount: number = 1) => errorMiddleware,
      fields: (_fields: { name: string; maxCount?: number }[]) =>
        errorMiddleware,
      array: (_fieldName: string, _maxCount: number = 10) => errorMiddleware,
    };
  }
};

const createBucketIfNotExists = async (bucketName: string) => {
  try {
    await s3.send(new HeadBucketCommand({ Bucket: bucketName }));
    // Bucket already exists
  } catch (error: any) {
    if (error.name === "NotFound" || error.name === "NoSuchBucket") {
      try {
        await s3.send(new CreateBucketCommand({ Bucket: bucketName }));
        console.log(`Bucket ${bucketName} created successfully`);
      } catch (createError: any) {
        console.error(`Error creating bucket: ${createError.message}`);
      }
    } else {
      console.error(`Error checking bucket: ${error.message}`);
    }
  }
};

/**
 * Move a file to a different path within the same bucket
 * @param bucketName - The bucket name
 * @param sourceKey - Current file path/key
 * @param destinationKey - New file path/key
 * @param item_id - Item ID to update
 * @param deleteSource - Whether to delete the source file after copying (default: true)
 * @returns Object containing information about the move operation
 */
const moveFileInBucket = async (
  bucketName: string,
  sourceKey: string,
  destinationKey: string,
  item_id: number,
  deleteSource: boolean = true
): Promise<{
  success: boolean;
  sourceUrl?: string;
  destinationUrl?: string;
  error?: any;
}> => {
  try {
    // Ensure bucket exists
    await createBucketIfNotExists(bucketName);

    // Check if source file exists
    try {
      await s3.send(
        new HeadObjectCommand({
          Bucket: bucketName,
          Key: sourceKey,
        })
      );
    } catch (error) {
      return {
        success: false,
        error: `Source file does not exist: ${bucketName}/${sourceKey}`,
      };
    }

    // Check if source and destination are the same
    if (sourceKey === destinationKey) {
      return {
        success: true,
        sourceUrl: `${sourceKey}`,
        destinationUrl: `${destinationKey}`,
      };
    }

    await Item.update(
      {
        item_location: destinationKey,
      },
      {
        where: {
          id: item_id,
        },
      }
    );

    // Copy the file to the destination path
    await s3.send(
      new CopyObjectCommand({
        CopySource: `${bucketName}/${sourceKey}`,
        Bucket: bucketName,
        Key: destinationKey,
      })
    );

    // Delete the source file if deleteSource is true
    if (deleteSource) {
      await s3.send(
        new DeleteObjectCommand({
          Bucket: bucketName,
          Key: sourceKey,
        })
      );
    }

    return {
      success: true,
      sourceUrl: `${sourceKey}`,
      destinationUrl: `${destinationKey}`,
    };
  } catch (error) {
    console.error("Error moving file within bucket:", error);
    return {
      success: false,
      error: error,
    };
  }
};

const deleteFileFromBucket = async (bucketName: string, sourceKey: string) => {
  try {
    await s3.send(
      new DeleteObjectCommand({
        Bucket: bucketName,
        Key: sourceKey,
      })
    );
    return {
      success: true,
    };
  } catch (error) {
    console.error("Error delete file from bucket:", error);
    return {
      success: false,
      error: error,
    };
  }
};

const moveFilesLocalToS3 = async (
  file: any,
  folderPath: string,
  _fileName: string,
  organization_id: string,
  bucketName: string = process.env.NODE_ENV!
) => {
  try {
    // Generate file hash to check for duplicates
    const fileHash: any = getHash(file);
    const fileName = file.originalname;
    const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

    // Upload file to S3
    await s3.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: filePath,
        Body: file.buffer,
        ContentType: file.mimetype,
      })
    );

    const saveItem: any = {
      item_type:
        file.mimetype == "multipart/form-data"
          ? item_type.VIDEO
          : file.mimetype == "application/octet-stream"
            ? item_type.VIDEO
            : file.mimetype.split("/")[0] == "application"
              ? "pdf"
              : file.mimetype.split("/")[0],
      item_name: file.filename,
      item_hash: fileHash?.hash,
      item_mime_type: file.mimetype,
      item_extension: path.extname(file.originalname),
      item_size: file.size,
      item_IEC: item_IEC.B,
      item_status: item_status.ACTIVE,
      item_external_location: item_external_location.NO,
      item_location: filePath,
      item_organization_id: organization_id,
    };

    const item = await Item.create(saveItem);
    return {
      success: true,
      data: item,
    };
  } catch (error) {
    return {
      success: false,
      error: error,
    };
  }
};

/**
 * Upload a file buffer directly to S3
 * @param bucketName - S3 bucket name
 * @param key - The file path/key within the bucket
 * @param fileBuffer - The file data buffer
 * @param contentType - The file MIME type
 * @returns Object containing upload status and error if any
 */
const uploadFileToBucket = async (
  bucketName: string,
  key: string,
  fileBuffer: Buffer,
  contentType: string = "application/octet-stream"
): Promise<{
  success: boolean;
  error?: any;
}> => {
  try {
    // Ensure bucket exists
    await createBucketIfNotExists(bucketName);

    // Upload to S3
    await s3.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
      })
    );

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error uploading file to bucket:", error);
    return {
      success: false,
      error,
    };
  }
};

// Default export object - exactly matching recipe pattern
export default {
  s3,
  multerS3,
  moveFileInBucket,
  deleteFileFromBucket,
  moveFilesLocalToS3,
  uploadFileToBucket,
};
