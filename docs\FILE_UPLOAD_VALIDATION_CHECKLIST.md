# File Upload Validation Checklist

## ✅ Pre-Deployment Validation

### 1. Basic File Upload Functionality
- [ ] **Create Ticket with Files**: Upload 1-5 files during ticket creation
- [ ] **File Types**: Test all supported file types (images, documents, videos, audio, archives)
- [ ] **File Names**: Test files with special characters, spaces, unicode characters
- [ ] **File Sizes**: Test files of various sizes (1KB, 1MB, 10MB, 50MB)
- [ ] **Multiple Files**: Upload multiple files simultaneously

### 2. File Management During Updates
- [ ] **Add Files**: Add new files to existing ticket
- [ ] **Remove Files**: Remove specific files by ID
- [ ] **Replace Files**: Replace all existing files with new ones
- [ ] **Mixed Operations**: Remove some files and add new ones in same request
- [ ] **No Changes**: Update ticket without file changes (should skip processing)

### 3. Validation & Security
- [ ] **File Type Validation**: Reject unsupported file types (.exe, .bat, .sh, etc.)
- [ ] **File Size Limits**: Reject files exceeding 50MB limit
- [ ] **File Count Limits**: Reject more than 5 files per ticket
- [ ] **Malicious Filenames**: Reject files with dangerous names (../../../etc/passwd)
- [ ] **Empty Files**: Handle empty files gracefully
- [ ] **Corrupted Files**: Handle corrupted file uploads

### 4. Error Handling
- [ ] **Network Errors**: Handle S3 connection failures
- [ ] **Database Errors**: Handle database transaction failures
- [ ] **Validation Errors**: Return clear error messages for validation failures
- [ ] **Partial Failures**: Rollback on partial upload failures
- [ ] **Timeout Handling**: Handle upload timeouts gracefully

### 5. Data Integrity
- [ ] **Transaction Safety**: All database operations in transactions
- [ ] **File-Database Sync**: Ensure files in S3 match database records
- [ ] **Duplicate Prevention**: Prevent duplicate file uploads (hash checking)
- [ ] **Orphaned Files**: No orphaned files in S3 or database
- [ ] **Concurrent Updates**: Handle concurrent file operations safely

### 6. Performance & Memory
- [ ] **Memory Usage**: Large file uploads don't cause memory leaks
- [ ] **Upload Speed**: Files upload within reasonable time
- [ ] **Parallel Processing**: Multiple file uploads processed efficiently
- [ ] **Resource Cleanup**: Temporary resources cleaned up after upload

### 7. API Response Format
- [ ] **Success Response**: Correct format with file details
- [ ] **Error Response**: Clear error messages and codes
- [ ] **File Metadata**: Correct file size, type, name in response
- [ ] **Attachment IDs**: Valid attachment IDs for future operations

## 🧪 Test Scenarios

### Scenario 1: Basic File Operations
```bash
# Create ticket with file
POST /v1/private/tickets/create
- ticketFiles: test.png (1MB)
- Expected: 1 attachment

# Add more files
PUT /v1/private/tickets/update/1
- ticketFiles: test1.pdf (2MB)
- attachment_action: add
- Expected: 2 attachments

# Remove specific file
PUT /v1/private/tickets/update/1
- remove_attachment_ids: [1]
- Expected: 1 attachment (test1.pdf remains)
```

### Scenario 2: Edge Cases
```bash
# Upload maximum files
POST /v1/private/tickets/create
- ticketFiles: file1.jpg, file2.pdf, file3.png, file4.docx, file5.zip
- Expected: 5 attachments

# Try to add 6th file (should fail)
PUT /v1/private/tickets/update/1
- ticketFiles: file6.txt
- Expected: Error "maximum 5 attachments"

# Replace all files
PUT /v1/private/tickets/update/1
- ticketFiles: new1.jpg, new2.pdf
- attachment_action: replace
- Expected: 2 attachments (all old files removed)
```

### Scenario 3: Error Cases
```bash
# Invalid file type
POST /v1/private/tickets/create
- ticketFiles: virus.exe
- Expected: Error "file type not allowed"

# File too large
POST /v1/private/tickets/create
- ticketFiles: huge.mp4 (60MB)
- Expected: Error "file size exceeds limit"

# Invalid remove IDs
PUT /v1/private/tickets/update/1
- remove_attachment_ids: [99999]
- Expected: Error "attachment not found"
```

## 🔍 Manual Testing Steps

### Step 1: Environment Setup
1. Ensure S3/MinIO is running and accessible
2. Database is connected and migrations are up to date
3. Test user has proper permissions
4. File upload limits are configured correctly

### Step 2: Basic Upload Test
1. Create a new ticket with 1 image file
2. Verify file appears in response
3. Check file exists in S3 bucket
4. Verify database record created

### Step 3: File Management Test
1. Update ticket to add another file
2. Verify both files present
3. Remove first file by ID
4. Verify only second file remains
5. Replace all files with new ones
6. Verify old files removed, new files added

### Step 4: Error Handling Test
1. Try uploading unsupported file type
2. Try uploading file larger than 50MB
3. Try uploading more than 5 files
4. Try removing non-existent attachment
5. Verify appropriate error messages

### Step 5: Performance Test
1. Upload 5 files simultaneously (total ~40MB)
2. Monitor memory usage during upload
3. Verify upload completes within 30 seconds
4. Check no memory leaks after upload

## 🚨 Critical Issues to Watch For

### Database Issues
- Orphaned attachment records without corresponding files
- Missing foreign key constraints
- Transaction rollback failures
- Concurrent modification conflicts

### File Storage Issues
- Files uploaded to S3 but not recorded in database
- Database records without corresponding S3 files
- Incorrect file paths or bucket names
- Permission issues accessing S3

### Memory Issues
- Memory leaks during large file uploads
- Buffer overflow with multiple simultaneous uploads
- Temporary files not cleaned up
- Process crashes under load

### Security Issues
- Malicious file uploads bypassing validation
- Directory traversal attacks via filenames
- Executable files uploaded and accessible
- Unauthorized access to other users' files

## ✅ Sign-off Checklist

Before marking file upload as production-ready:

- [ ] All test scenarios pass
- [ ] No memory leaks detected
- [ ] Error handling covers all edge cases
- [ ] Security validation prevents malicious uploads
- [ ] Performance meets requirements (< 30s for 5x10MB files)
- [ ] Database transactions work correctly
- [ ] S3 integration is stable
- [ ] API responses are consistent
- [ ] Documentation is complete and accurate
- [ ] Code review completed by senior developer

**Tested by**: ________________  
**Date**: ________________  
**Environment**: ________________  
**Version**: ________________
