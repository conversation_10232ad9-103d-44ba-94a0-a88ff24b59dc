import { celebrate, Joi, Segments } from "celebrate";
import {
  TICKET_MODULE,
  TICKET_TYPE,
  TICKET_PRIORITY,
  TICKET_STATUS,
  SORT_ORDER,
  SORT_BY,
  VALIDATION_CONSTANT,
} from "../helper/constant";
import { isDefaultAccess, isAgent } from "../utils/common";

// Dynamic validator that adjusts based on user role
const createTicketValidator = () => {
  return async (req: any, res: any, next: any) => {
    try {
      const userId = req.user?.id;

      // Check user roles
      let isAgentUser = false;
      let isSuperAdminUser = false;

      if (userId) {
        try {
          isAgentUser = await isAgent(userId);
          isSuperAdminUser = await isDefaultAccess(userId);

          // Debug logging
          console.log(`🔍 VALIDATOR DEBUG - User ID: ${userId}`);
          console.log(`🔍 VALIDATOR DEBUG - isAgent: ${isAgentUser}`);
          console.log(
            `🔍 VALIDATOR DEBUG - isDefaultAccess: ${isSuperAdminUser}`
          );
        } catch (error) {
          console.error("Error checking user roles in validator:", error);
        }
      }

      // Base validation schema
      let validationSchema = Joi.object().keys({
        ticket_title: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_TITLE_MIN)
          .max(VALIDATION_CONSTANT.TICKET_TITLE_MAX)
          .required(),
        ticket_description: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MIN)
          .max(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MAX)
          .required(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .required(),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .required(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .required(),
        support_pin: Joi.string().min(1).max(50).optional(), // Optional for admin users
        assigned_to_user_id: Joi.alternatives()
          .try(
            Joi.number().integer().positive(),
            Joi.string().allow("").optional(), // Allow empty string
            Joi.allow(null) // Allow null values
          )
          .optional()
          .messages({
            "alternatives.match":
              "Assigned user ID must be a valid positive number or empty",
          }), // Optional assignment
        // Contact information overrides (optional manual input)
        ticket_owner_name: Joi.string().max(255).optional().allow(""),
        ticket_owner_email: Joi.string().email().max(255).optional().allow(""),
        ticket_owner_phone: Joi.string().max(50).optional().allow(""),
        // Admin-specific field for organization selection
        organization_id: Joi.string().optional(),
        // File upload fields are handled by multer middleware
        ticketFiles: Joi.any().optional(),
      });

      // For ticket creation, manual dates are optional for all users
      // System will use defaults or SLA calculations
      validationSchema = validationSchema.keys({
        manual_start_date: Joi.alternatives()
          .try(
            Joi.date().iso(),
            Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
            Joi.string().pattern(/^\d{2}\/\d{2}\/\d{4}$/), // DD/MM/YYYY
            Joi.string().isoDate(),
            Joi.string().allow("").optional() // Allow empty string
          )
          .optional(),
        manual_due_date: Joi.alternatives()
          .try(
            Joi.date().iso(),
            Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
            Joi.string().pattern(/^\d{2}\/\d{2}\/\d{4}$/), // DD/MM/YYYY
            Joi.string().isoDate(),
            Joi.string().allow("").optional() // Allow empty string
          )
          .optional(),
        estimated_hours: Joi.alternatives()
          .try(
            Joi.number().min(0).max(1000),
            Joi.string().allow("").optional() // Allow empty string
          )
          .optional(),
      });

      // Apply validation
      const celebrateValidator = celebrate({
        [Segments.BODY]: validationSchema.unknown(true), // Allow unknown fields for file upload
      });

      celebrateValidator(req, res, next);
    } catch (error) {
      console.error("Error in createTicketValidator:", error);
      return res.status(400).json({
        status: false,
        message: "Validation error",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };
};

// Dynamic update validator that adjusts based on user role
const updateTicketValidator = () => {
  return async (req: any, res: any, next: any) => {
    try {
      const userId = req.user?.id;

      // Check user roles
      let isAgentUser = false;
      let isSuperAdminUser = false;

      if (userId) {
        try {
          isAgentUser = await isAgent(userId);
          isSuperAdminUser = await isDefaultAccess(userId);

          // Debug logging
          console.log(`🔍 UPDATE VALIDATOR DEBUG - User ID: ${userId}`);
          console.log(`🔍 UPDATE VALIDATOR DEBUG - isAgent: ${isAgentUser}`);
          console.log(
            `🔍 UPDATE VALIDATOR DEBUG - isDefaultAccess: ${isSuperAdminUser}`
          );
        } catch (error) {
          console.error(
            "Error checking user roles in update validator:",
            error
          );
        }
      }

      // Base validation schema for updates
      let validationSchema = Joi.object().keys({
        ticket_title: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_TITLE_MIN)
          .max(VALIDATION_CONSTANT.TICKET_TITLE_MAX)
          .optional(),
        ticket_description: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MIN)
          .max(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MAX)
          .optional(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .optional(),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .optional(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .optional(),
        ticket_status: Joi.string()
          .valid(...Object.values(TICKET_STATUS))
          .optional(),
        assigned_to_user_id: Joi.alternatives()
          .try(
            Joi.number().integer().positive(),
            Joi.string().allow("").optional(), // Allow empty string
            Joi.allow(null) // Allow null values
          )
          .optional()
          .messages({
            "alternatives.match":
              "Assigned user ID must be a valid positive number or empty",
          }),
        resolution_note: Joi.string()
          .min(10)
          .max(VALIDATION_CONSTANT.RESOLUTION_NOTE_MAX_LENGTH)
          .optional(),
        rating: Joi.number()
          .integer()
          .min(VALIDATION_CONSTANT.RATING_MIN)
          .max(VALIDATION_CONSTANT.RATING_MAX)
          .optional(),
        review_comment: Joi.string()
          .max(VALIDATION_CONSTANT.REVIEW_COMMENT_MAX_LENGTH)
          .optional(),
        // PIN verification for updates
        support_pin: Joi.string().min(1).max(50).optional(),
        // Followers management
        followers: Joi.array()
          .items(Joi.number().integer().positive())
          .optional(),
        date_override_reason: Joi.string().max(255).optional(),
        // Time tracking fields
        estimated_hours: Joi.alternatives()
          .try(
            Joi.number().min(0).max(1000),
            Joi.allow(null),
            Joi.string().allow("").optional()
          )
          .optional(),
        actual_hours: Joi.number().min(0).max(1000).optional(),
        // SLA pause functionality
        sla_paused: Joi.boolean().optional(),
        sla_pause_reason: Joi.string().max(255).optional(),
        // File upload fields are handled by multer middleware
        ticketFiles: Joi.any().optional(),
      });

      // Add flexible date validation that accepts multiple formats
      validationSchema = validationSchema.keys({
        manual_start_date: Joi.alternatives()
          .try(
            Joi.date().iso(),
            Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
            Joi.string().pattern(/^\d{2}\/\d{2}\/\d{4}$/), // DD/MM/YYYY
            Joi.string().isoDate(),
            Joi.string().allow("").optional(), // Allow empty string
            Joi.allow(null) // Allow null values
          )
          .optional()
          .messages({
            "alternatives.match":
              "Start date must be a valid date format (ISO, YYYY-MM-DD, or DD/MM/YYYY)",
          }),
        manual_due_date: Joi.alternatives()
          .try(
            Joi.date().iso(),
            Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
            Joi.string().pattern(/^\d{2}\/\d{2}\/\d{4}$/), // DD/MM/YYYY
            Joi.string().isoDate(),
            Joi.string().allow("").optional(), // Allow empty string
            Joi.allow(null) // Allow null values
          )
          .optional()
          .messages({
            "alternatives.match":
              "Due date must be a valid date format (ISO, YYYY-MM-DD, or DD/MM/YYYY)",
          }),
      });

      // Apply validation
      const celebrateValidator = celebrate({
        [Segments.BODY]: validationSchema.unknown(true), // Allow unknown fields for file upload
        [Segments.PARAMS]: Joi.object().keys({
          id: Joi.number().integer().positive().required(),
        }),
      });

      celebrateValidator(req, res, next);
    } catch (error) {
      console.error("Error in updateTicketValidator:", error);
      return res.status(400).json({
        status: false,
        message: "Validation error",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };
};

const getTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const deleteTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getTicketsListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      organization_id: Joi.string().optional(),
      ticket_status: Joi.string()
        .valid(...Object.values(TICKET_STATUS))
        .optional(),
      ticket_priority: Joi.string()
        .valid(...Object.values(TICKET_PRIORITY))
        .optional(),
      ticket_module: Joi.string()
        .valid(...Object.values(TICKET_MODULE))
        .optional(),
      ticket_type: Joi.string()
        .valid(...Object.values(TICKET_TYPE))
        .optional(),
      assigned_to_user_id: Joi.alternatives()
        .try(
          Joi.number().integer(),
          Joi.number().valid(-1) // Special case for unassigned tickets
        )
        .optional(),
      ticket_owner_user_id: Joi.number().integer().positive().optional(),
      search: Joi.string().max(255).optional(),
      date_from: Joi.date().iso().optional(),
      date_to: Joi.date().iso().optional(),
      overdue: Joi.boolean().optional(),
      sort_by: Joi.string()
        .valid(...Object.values(SORT_BY))
        .optional(),
      sort_order: Joi.string()
        .valid(...Object.values(SORT_ORDER))
        .optional(),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    }),
  });

const assignTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      assigned_to_user_id: Joi.number().required(),
      auto_start_work: Joi.boolean().optional(), // Option to immediately start work
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const resolveTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      resolution_note: Joi.string()
        .min(10)
        .max(VALIDATION_CONSTANT.RESOLUTION_NOTE_MAX_LENGTH)
        .required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const rateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      rating: Joi.number()
        .integer()
        .min(VALIDATION_CONSTANT.RATING_MIN)
        .max(VALIDATION_CONSTANT.RATING_MAX)
        .required(),
      review_comment: Joi.string()
        .max(VALIDATION_CONSTANT.REVIEW_COMMENT_MAX_LENGTH)
        .optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const addTicketCommentValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        comment_text: Joi.string()
          .min(1)
          .max(VALIDATION_CONSTANT.MESSAGE_CONTENT_MAX)
          .required(),
        is_private: Joi.boolean().default(false),
      })
      .unknown(true), // Allow file upload fields
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const getTicketCommentsValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
    [Segments.QUERY]: Joi.object()
      .keys({
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
      })
      .unknown(true),
  });

const getTicketHistoryValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

export default {
  createTicketValidator,
  updateTicketValidator,
  getTicketValidator,
  deleteTicketValidator,
  getTicketsListValidator,
  assignTicketValidator,
  resolveTicketValidator,
  rateTicketValidator,
  addTicketCommentValidator,
  getTicketCommentsValidator,
  getTicketHistoryValidator,
};
