import { QueryTypes } from "sequelize";
import { sequelize, db } from "../models";
import { TICKET_PRIORITY } from "./constant";
import { getFollowersDetails } from "../utils/common";

/**
 * Check if SLA should be recalculated (simple optimization)
 */
const shouldRecalculateSLA = (ticket: any): boolean => {
  // Only recalculate if ticket is still active and was created recently
  const isActive = !["resolved", "closed"].includes(ticket.ticket_status);
  const isRecent =
    new Date().getTime() - new Date(ticket.created_at).getTime() <
    24 * 60 * 60 * 1000; // 24 hours
  return isActive && isRecent;
};

/**
 * Validate time tracking data integrity
 */
export const validateTimeTrackingData = (ticket: any): boolean => {
  try {
    // Check if work_started_at is before work_completed_at
    if (ticket.work_started_at && ticket.work_completed_at) {
      const startTime = new Date(ticket.work_started_at).getTime();
      const endTime = new Date(ticket.work_completed_at).getTime();
      if (startTime >= endTime) {
        console.warn(
          `Invalid time tracking for ticket ${ticket.id}: start time >= end time`
        );
        return false;
      }
    }

    // Check if actual_hours is reasonable
    if (ticket.actual_hours !== null && ticket.actual_hours !== undefined) {
      if (ticket.actual_hours < 0 || ticket.actual_hours > 1000) {
        console.warn(
          `Invalid actual_hours for ticket ${ticket.id}: ${ticket.actual_hours}`
        );
        return false;
      }
    }

    // Check if estimated_hours is reasonable
    if (
      ticket.estimated_hours !== null &&
      ticket.estimated_hours !== undefined
    ) {
      if (ticket.estimated_hours < 0 || ticket.estimated_hours > 1000) {
        console.warn(
          `Invalid estimated_hours for ticket ${ticket.id}: ${ticket.estimated_hours}`
        );
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("Error validating time tracking data:", error);
    return false;
  }
};

// Cache for organization names to avoid repeated DB queries
const orgNameCache = new Map<string, { name: string; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

/**
 * Get organization name from ORG table with caching
 */
export const getOrganizationName = async (
  organizationId: string
): Promise<string> => {
  try {
    if (!organizationId) return organizationId;

    // Check cache first
    const cached = orgNameCache.get(organizationId);
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      return cached.name;
    }

    // Query the ORG table to get the actual organization name
    const query = `
      SELECT o.NAME as organization_name, o.ALIAS as organization_alias
      FROM ORG o
      WHERE o.ID = :organizationId
      LIMIT 1
    `;

    const result = (await sequelize.query(query, {
      replacements: {
        organizationId: organizationId,
      },
      type: QueryTypes.SELECT,
    })) as any[];

    let orgName = organizationId; // Default fallback

    if (result && result.length > 0) {
      // Return the name, or alias as fallback, or ID as final fallback
      orgName = (
        result[0].organization_name ||
        result[0].organization_alias ||
        organizationId
      );
    } else {
      console.warn(`Organization not found in ORG table: ${organizationId}`);
    }

    // Cache the result
    orgNameCache.set(organizationId, {
      name: orgName,
      timestamp: Date.now()
    });

    return orgName;
  } catch (error) {
    console.error("Error getting organization name:", error);
    return organizationId;
  }
};

/**
 * Get ticket by ID using raw query with user details (following recipe-ms pattern)
 * @param ticketId - Ticket ID
 * @param organizationId - Organization ID for filtering
 * @returns Complete ticket data with user information
 */
export const getTicketByIdRaw = async (
  ticketId: number,
  organizationId?: string | null
): Promise<any> => {
  try {
    const baseUrl = global.config?.API_BASE_URL || "";

    const query = `
      SELECT
        t.*,
        -- Assigned user details
        assigned.user_email as assigned_email,
        CONCAT(assigned.user_first_name, ' ', assigned.user_last_name) as assigned_full_name,
        assigned.user_avatar as assigned_avatar,
        IF((assigned.user_avatar IS NOT NULL AND assigned.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = assigned.user_avatar)),
           '') AS assigned_avatar_url,

        -- Resolved by user details
        resolved.user_email as resolved_email,
        CONCAT(resolved.user_first_name, ' ', resolved.user_last_name) as resolved_full_name,
        resolved.user_avatar as resolved_avatar,
        IF((resolved.user_avatar IS NOT NULL AND resolved.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = resolved.user_avatar)),
           '') AS resolved_avatar_url,

        -- Created by user details
        creator.user_email as creator_email,
        CONCAT(creator.user_first_name, ' ', creator.user_last_name) as creator_full_name,
        creator.user_avatar as creator_avatar,
        IF((creator.user_avatar IS NOT NULL AND creator.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = creator.user_avatar)),
           '') AS creator_avatar_url,

        -- Rated by user details
        rater.user_email as rater_email,
        CONCAT(rater.user_first_name, ' ', rater.user_last_name) as rater_full_name,
        rater.user_avatar as rater_avatar,
        IF((rater.user_avatar IS NOT NULL AND rater.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = rater.user_avatar)),
           '') AS rater_avatar_url

      FROM mo_support_tickets t
      LEFT JOIN nv_users assigned ON t.assigned_to_user_id = assigned.id
      LEFT JOIN nv_users resolved ON t.resolved_by_user_id = resolved.id
      LEFT JOIN nv_users creator ON t.created_by_user_id = creator.id
      LEFT JOIN nv_users rater ON t.rated_by_user_id = rater.id
      WHERE t.id = :ticketId
        ${organizationId ? "AND t.organization_id = :organizationId" : ""}
        AND t.deleted_at IS NULL
    `;

    const replacements: any = { ticketId };
    if (organizationId) {
      replacements.organizationId = organizationId;
    }

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements,
    });

    if (!result || result.length === 0) {
      return null;
    }

    const ticket = result[0];

    // Optimize: Batch all async operations
    const [orgConfig, attachments, comments, organizationName] = await Promise.all([
      // Only fetch config if needed for SLA recalculation
      (!ticket.sla_due_date || shouldRecalculateSLA(ticket))
        ? db.SupportConfig.findOne({ where: { organization_id: organizationId }, raw: true })
        : Promise.resolve(null),
      // Get attachments for this ticket
      getTicketAttachments(ticket.id),
      // Get comments for this ticket
      getTicketCommentsRaw(ticket.id),
      // Get organization name
      getOrganizationName(ticket.organization_id)
    ]);

    // Only recalculate SLA if needed
    if (orgConfig && (!ticket.sla_due_date || shouldRecalculateSLA(ticket))) {
      try {
        // Only calculate if SLA is enabled
        if (orgConfig.sla_enabled) {
          const newSlaDate = calculateSlaDueDate(
            ticket.created_at,
            ticket.ticket_priority,
            orgConfig
          );
          if (newSlaDate) {
            ticket.sla_due_date = newSlaDate;
          }
        } else if (orgConfig.sla_enabled === false) {
          // SLA is disabled - ensure sla_due_date is null
          ticket.sla_due_date = null;
        }
      } catch (slaErr) {
        console.error("SLA recalculation error:", slaErr);
      }
    }

    // Get followers details
    let followersDetails = [];
    if (ticket.followers && Array.isArray(ticket.followers)) {
      followersDetails = await getFollowersDetails(ticket.followers);
    }

    return {
      ...ticket,
      organization_name: organizationName,
      followers: followersDetails, // Replace the array of IDs with detailed user info
      attachments,
      comments,
    };
  } catch (error) {
    console.error("Error in getTicketByIdRaw:", error);
    throw error;
  }
};

/**
 * Get ticket by slug using raw query with user details (following recipe-ms pattern)
 * @param ticketSlug - Ticket slug
 * @param organizationId - Organization ID for filtering
 * @returns Complete ticket data with user information
 */
export const getTicketBySlugRaw = async (
  ticketSlug: string,
  organizationId?: string | null
): Promise<any> => {
  try {
    const baseUrl = global.config?.API_BASE_URL || "";

    const query = `
      SELECT
        t.*,

        -- Assigned user details
        assigned.user_email as assigned_email,
        CONCAT(assigned.user_first_name, ' ', assigned.user_last_name) as assigned_full_name,
        assigned.user_avatar as assigned_avatar,
        IF((assigned.user_avatar IS NOT NULL AND assigned.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = assigned.user_avatar)),
           '') AS assigned_avatar_url,

        -- Resolved by user details
        resolved.user_email as resolved_email,
        CONCAT(resolved.user_first_name, ' ', resolved.user_last_name) as resolved_full_name,
        resolved.user_avatar as resolved_avatar,
        IF((resolved.user_avatar IS NOT NULL AND resolved.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = resolved.user_avatar)),
           '') AS resolved_avatar_url,

        -- Created by user details
        creator.user_email as creator_email,
        CONCAT(creator.user_first_name, ' ', creator.user_last_name) as creator_full_name,
        creator.user_avatar as creator_avatar,
        IF((creator.user_avatar IS NOT NULL AND creator.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = creator.user_avatar)),
           '') AS creator_avatar_url,

        -- Rated by user details
        rater.user_email as rater_email,
        CONCAT(rater.user_first_name, ' ', rater.user_last_name) as rater_full_name,
        rater.user_avatar as rater_avatar,
        IF((rater.user_avatar IS NOT NULL AND rater.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = rater.user_avatar)),
           '') AS rater_avatar_url

      FROM mo_support_tickets t
      LEFT JOIN nv_users assigned ON t.assigned_to_user_id = assigned.id
      LEFT JOIN nv_users resolved ON t.resolved_by_user_id = resolved.id
      LEFT JOIN nv_users creator ON t.created_by_user_id = creator.id
      LEFT JOIN nv_users rater ON t.rated_by_user_id = rater.id
      WHERE t.ticket_slug = :ticketSlug
        ${organizationId ? "AND t.organization_id = :organizationId" : ""}
        AND t.deleted_at IS NULL
    `;

    const replacements: any = { ticketSlug };
    if (organizationId) {
      replacements.organizationId = organizationId;
    }

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements,
    });

    if (!result || result.length === 0) {
      return null;
    }

    const ticket = result[0];

    // Recalculate SLA due date with latest config (only if SLA is enabled)
    try {
      const orgConfig = await db.SupportConfig.findOne({
        where: { organization_id: organizationId },
      });

      // Only recalculate if SLA is enabled, otherwise preserve the stored value
      if (orgConfig && orgConfig.sla_enabled) {
        ticket.sla_due_date = calculateSlaDueDate(
          ticket.created_at,
          ticket.ticket_priority,
          orgConfig
        );
      } else if (orgConfig && orgConfig.sla_enabled === false) {
        // SLA is explicitly disabled - ensure sla_due_date is null
        ticket.sla_due_date = null;
      }
    } catch (slaErr) {
      console.error("SLA recalculation error:", slaErr);
    }

    // Get attachments for this ticket
    const attachments = await getTicketAttachments(ticket.id);
    const comments = await getTicketCommentsRaw(ticket.id);

    // Get organization name from Keycloak
    const organizationName = await getOrganizationName(ticket.organization_id);

    // Get followers details
    let followersDetails = [];
    if (ticket.followers && Array.isArray(ticket.followers)) {
      followersDetails = await getFollowersDetails(ticket.followers);
    }

    // Resolve contact information (priority: manual override > nv_users data)
    const resolvedContact = {
      ticket_owner_name:
        ticket.ticket_owner_name ||
        ticket.owner_full_name ||
        ticket.owner_email ||
        "Unknown User",
      ticket_owner_email: ticket.ticket_owner_email || ticket.owner_email || "",
      ticket_owner_phone: ticket.ticket_owner_phone || ticket.owner_phone || "",
    };

    return {
      ...ticket,
      organization_name: organizationName,
      followers: followersDetails, // Replace the array of IDs with detailed user info
      // Replace the separate fields with resolved contact info
      ...resolvedContact,
      // Keep the original nv_users data for reference (optional)
      owner_details: {
        owner_email: ticket.owner_email,
        owner_phone: ticket.owner_phone,
        owner_full_name: ticket.owner_full_name,
        owner_avatar: ticket.owner_avatar,
      },
      attachments,
      comments,
    };
  } catch (error) {
    console.error("Error in getTicketBySlugRaw:", error);
    throw error;
  }
};

/**
 * Get tickets list using raw query with pagination (following recipe-ms pattern)
 * @param organizationId - Organization ID for filtering
 * @param options - Query options (limit, offset, search, filters)
 * @returns Paginated tickets list with user information
 */
export const getTicketsListRaw = async (
  organizationId: string | undefined,
  options: {
    limit?: number;
    offset?: number;
    search?: string;
    ticket_status?: string;
    ticket_priority?: string;
    ticket_type?: string;
    ticket_module?: string;
    assigned_to_user_id?: number;
    ticket_owner_user_id?: number;
    date_from?: string;
    date_to?: string;
    overdue?: boolean;
    sort_by?: string;
    sort_order?: string;
    followerUserId?: number; // Add follower access support
  } = {}
): Promise<{ tickets: any[]; total: number }> => {
  try {
    const {
      limit,
      offset,
      search,
      ticket_status,
      ticket_priority,
      ticket_type,
      ticket_module,
      assigned_to_user_id,
      ticket_owner_user_id,
      date_from,
      date_to,
      overdue,
      sort_by = "created_at",
      sort_order = "DESC",
      followerUserId,
    } = options;

    // Debug logging for filters
    console.log("getTicketsListRaw - Applied filters:", {
      organizationId,
      search,
      ticket_status,
      ticket_priority,
      ticket_type,
      ticket_module,
      assigned_to_user_id,
      ticket_owner_user_id,
      date_from,
      date_to,
      overdue,
      sort_by,
      sort_order,
    });

    // Build WHERE conditions. Always exclude deleted tickets. Apply organization filter only if provided
    const whereConditions = ["t.deleted_at IS NULL"];
    const replacements: any = {};

    // Build access conditions: organization OR follower access OR assigned access
    const accessConditions: string[] = [];

    if (organizationId) {
      accessConditions.push("t.organization_id = :organizationId");
      replacements.organizationId = organizationId;
    }

    // Optimize: For better performance, prioritize assigned_to_user_id (indexed) over JSON_CONTAINS (slow)
    if (assigned_to_user_id) {
      accessConditions.push("t.assigned_to_user_id = :assigned_to_user_id");
      replacements.assigned_to_user_id = assigned_to_user_id;
    }

    if (followerUserId) {
      accessConditions.push("JSON_CONTAINS(t.followers, :followerUserIdJson)");
      replacements.followerUserIdJson = JSON.stringify(followerUserId);
    }

    // If we have access conditions, combine them with OR
    if (accessConditions.length > 0) {
      whereConditions.push(`(${accessConditions.join(" OR ")})`);
    }

    if (search) {
      whereConditions.push(
        "(t.ticket_title LIKE :search OR t.ticket_description LIKE :search OR t.ticket_slug LIKE :search OR t.ticket_owner_name LIKE :search OR t.ticket_owner_email LIKE :search)"
      );
      replacements.search = `%${search}%`;
    }

    // Filter by ticket status
    if (ticket_status && ticket_status.trim()) {
      whereConditions.push("t.ticket_status = :ticket_status");
      replacements.ticket_status = ticket_status.trim();
    }

    // Filter by ticket priority
    if (ticket_priority && ticket_priority.trim()) {
      whereConditions.push("t.ticket_priority = :ticket_priority");
      replacements.ticket_priority = ticket_priority.trim();
    }

    // Filter by ticket type
    if (ticket_type && ticket_type.trim()) {
      whereConditions.push("t.ticket_type = :ticket_type");
      replacements.ticket_type = ticket_type.trim();
    }

    // Filter by ticket module
    if (ticket_module && ticket_module.trim()) {
      whereConditions.push("t.ticket_module = :ticket_module");
      replacements.ticket_module = ticket_module.trim();
    }

    // Filter by assigned user (only for special cases like unassigned tickets)
    if (assigned_to_user_id === -1) {
      // Special case: unassigned tickets
      whereConditions.push("t.assigned_to_user_id IS NULL");
    }
    // Note: Regular assigned_to_user_id filtering is now handled in access conditions above

    // Filter by ticket owner user
    if (ticket_owner_user_id) {
      whereConditions.push("t.ticket_owner_user_id = :ticket_owner_user_id");
      replacements.ticket_owner_user_id = ticket_owner_user_id;
    }

    // Filter by date range
    if (date_from) {
      whereConditions.push("DATE(t.created_at) >= :date_from");
      replacements.date_from = date_from;
    }

    if (date_to) {
      whereConditions.push("DATE(t.created_at) <= :date_to");
      replacements.date_to = date_to;
    }

    // Filter for overdue tickets
    if (overdue === true) {
      whereConditions.push("t.sla_due_date IS NOT NULL AND t.sla_due_date < NOW() AND t.ticket_status NOT IN ('resolved', 'closed')");
    }

    // Build ORDER BY clause with enhanced sort options
    const validSortFields = [
      "created_at",
      "updated_at",
      "ticket_priority",
      "ticket_status",
      "ticket_title",
      "ticket_owner_name",
      "assigned_at",
      "resolved_at",
      "sla_due_date",
      "id",
    ];
    const validSortOrders = ["ASC", "DESC"];

    const sortField = validSortFields.includes(sort_by)
      ? sort_by
      : "created_at";
    const sortDirection = validSortOrders.includes(sort_order?.toUpperCase())
      ? sort_order.toUpperCase()
      : "DESC";

    // Main query for tickets - optimized for performance
    const baseQuery = `
      SELECT
        -- Essential ticket fields only
        t.id,
        t.ticket_slug,
        t.organization_id,
        t.ticket_owner_user_id,
        t.ticket_owner_name,
        t.ticket_title,
        t.ticket_description,
        t.ticket_module,
        t.ticket_type,
        t.ticket_priority,
        t.ticket_status,
        t.sla_due_date,
        t.assigned_to_user_id,
        t.followers,
        t.created_by_user_id,
        t.updated_by_user_id,
        t.created_at,
        t.updated_at

      FROM mo_support_tickets t
      WHERE ${whereConditions.join(" AND ")}
      ORDER BY t.${sortField} ${sortDirection}
    `;

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM mo_support_tickets t
      WHERE ${whereConditions.join(" AND ")}
    `;

    // Performance monitoring
    const queryStartTime = Date.now();

    // Execute queries in parallel for better performance
    const [tickets, countResult] = await Promise.all([
      sequelize.query(
        limit ? `${baseQuery} LIMIT :limit OFFSET :offset` : baseQuery,
        {
          type: QueryTypes.SELECT,
          replacements: limit
            ? { ...replacements, limit, offset: offset || 0 }
            : replacements,
          // Add query optimization hints
          logging: process.env.NODE_ENV === 'development' ? console.log : false,
        }
      ),
      sequelize.query(countQuery, {
        type: QueryTypes.SELECT,
        replacements,
        logging: process.env.NODE_ENV === 'development' ? console.log : false,
      }),
    ]);

    const total = (countResult[0] as any)?.total || 0;

    // Performance monitoring
    const queryEndTime = Date.now();
    const queryDuration = queryEndTime - queryStartTime;

    if (queryDuration > 1000) {
      console.warn(`⚠️  SLOW QUERY: ${queryDuration}ms - Query: ${baseQuery.substring(0, 100)}...`);
      console.warn(`⚠️  Filters applied:`, { organizationId, search, ticket_status, ticket_priority });
    } else if (queryDuration > 500) {
      console.log(`⏱️  Query took ${queryDuration}ms`);
    }

    // Optimize: Batch process organization data and SLA configs
    if (tickets && tickets.length > 0) {
      // Get unique organization IDs
      const uniqueOrgIds = [...new Set((tickets as any[]).map(t => t.organization_id))];

      // Batch fetch organization configs and names
      const [orgConfigs, orgNames] = await Promise.all([
        // Fetch all org configs in one query
        db.SupportConfig.findAll({
          where: { organization_id: uniqueOrgIds },
          raw: true
        }),
        // Batch fetch organization names
        Promise.all(uniqueOrgIds.map(orgId =>
          getOrganizationName(orgId).then(name => ({ orgId, name }))
        ))
      ]);

      // Create lookup maps for performance
      const orgConfigMap = new Map();
      orgConfigs.forEach((config: any) => {
        orgConfigMap.set(config.organization_id, config);
      });

      const orgNameMap = new Map();
      orgNames.forEach(({ orgId, name }) => {
        orgNameMap.set(orgId, name);
      });

      // Get all unique follower IDs for batch processing
      const allFollowerIds = new Set<number>();
      (tickets as any[]).forEach(t => {
        if (t.followers && Array.isArray(t.followers)) {
          t.followers.forEach((id: number) => allFollowerIds.add(id));
        }
      });

      // Batch fetch follower details if any exist
      const followerDetailsMap = new Map();
      if (allFollowerIds.size > 0) {
        const followerDetails = await getFollowersDetails(Array.from(allFollowerIds));
        followerDetails.forEach((detail: any) => {
          followerDetailsMap.set(detail.id, detail);
        });
      }

      // Process tickets with cached data
      for (const t of tickets as any[]) {
        try {
          const orgConfig = orgConfigMap.get(t.organization_id);

          // Only recalculate SLA if enabled, otherwise preserve stored value
          if (orgConfig && orgConfig.sla_enabled) {
            t.sla_due_date = calculateSlaDueDate(
              t.created_at,
              t.ticket_priority,
              orgConfig
            );
          } else if (orgConfig && orgConfig.sla_enabled === false) {
            // SLA is explicitly disabled - ensure sla_due_date is null
            t.sla_due_date = null;
          }

          // Add organization name from cache
          t.organization_name = orgNameMap.get(t.organization_id) || t.organization_id;

          // Add followers details from cache
          if (t.followers && Array.isArray(t.followers)) {
            t.followers = t.followers.map((id: number) =>
              followerDetailsMap.get(id) || { id, name: 'Unknown User' }
            );
          } else {
            t.followers = [];
          }
        } catch (slaErr) {
          console.error("SLA recalculation error:", slaErr);
        }
      }
    }

    // Return the results
    const ticketsResult = tickets || [];

    return {
      tickets: ticketsResult,
      total: parseInt(total.toString(), 10),
    };
  } catch (error) {
    console.error("Error in getTicketsListRaw:", error);
    throw error;
  }
};

/**
 * Generate unique ticket slug
 * @returns Unique ticket slug
 */
export const generateTicketSlug = async (): Promise<string> => {
  try {
    const year = new Date().getFullYear();
    const prefix = `TKT-${year}`;

    // Get the latest ticket number globally (not per organization) to ensure uniqueness
    const query = `
      SELECT ticket_slug
      FROM mo_support_tickets
      WHERE ticket_slug LIKE :pattern
      ORDER BY id DESC
      LIMIT 1
    `;

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        pattern: `${prefix}-%`,
      },
    });

    let nextNumber = 1;
    if (result && result.length > 0) {
      const lastSlug = (result[0] as any).ticket_slug;
      const lastNumber = parseInt(lastSlug.split("-").pop() || "0");
      nextNumber = lastNumber + 1;
    }

    // Generate slug and check for uniqueness (with retry mechanism)
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const candidateSlug = `${prefix}-${(nextNumber + attempts).toString().padStart(3, "0")}`;

      // Check if this slug already exists
      const existingQuery = `
        SELECT COUNT(*) as count
        FROM mo_support_tickets
        WHERE ticket_slug = :slug
      `;

      const existingResult = await sequelize.query(existingQuery, {
        type: QueryTypes.SELECT,
        replacements: { slug: candidateSlug },
      });

      const count = (existingResult[0] as any).count;
      if (count === 0) {
        return candidateSlug; // Found unique slug
      }

      attempts++;
    }

    // If we couldn't find a unique slug, add timestamp
    const timestamp = Date.now().toString().slice(-4);
    return `${prefix}-${nextNumber.toString().padStart(3, "0")}-${timestamp}`;
  } catch (error) {
    console.error("Error generating ticket slug:", error);
    throw error;
  }
};

/**
 * Get ticket attachments with file URLs
 * @param ticketId - Ticket ID
 * @returns Array of attachment objects with download URLs
 */
export const getTicketAttachments = async (
  ticketId: number
): Promise<any[]> => {
  try {
    const baseUrl = global.config?.API_BASE_URL || "";

    const query = `
      SELECT
        ta.id,
        ta.ticket_id,
        i.item_name as file_name,
        i.item_size as file_size,
        i.item_type as file_type,
        CONCAT('${baseUrl}/', i.item_location) as download_url,
        ta.created_at as uploaded_at,
        -- Uploader details (simplified)
        CONCAT(uploader.user_first_name, ' ', uploader.user_last_name) as uploaded_by,
        uploader.user_avatar as uploader_avatar,
        IF((uploader.user_avatar IS NOT NULL AND uploader.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = uploader.user_avatar)),
           '') AS uploader_avatar_url
      FROM mo_support_ticket_attachments ta
      LEFT JOIN nv_items i ON ta.item_id = i.id
      LEFT JOIN nv_users uploader ON ta.created_by = uploader.id
      WHERE ta.ticket_id = :ticketId
      ORDER BY ta.created_at ASC
    `;

    const attachments = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: { ticketId },
    });

    return attachments || [];
  } catch (error) {
    console.error("Error in getTicketAttachments:", error);
    return [];
  }
};

export const getTicketCommentsRaw = async (
  ticketId: number,
  includePrivate: boolean = false
): Promise<any[]> => {
  try {
    const baseUrl = global.config?.API_BASE_URL || "";

    const query = `
      SELECT
        tm.id,
        tm.ticket_id,
        tm.message_text AS comment_text,
        tm.message_type,
        tm.is_private,
        tm.attachment_id,
        tm.created_by,
        tm.created_at,
        -- Add sender full name and avatar
        CONCAT(u.user_first_name, ' ', u.user_last_name) as sender_name,
        u.user_email as sender_email,
        u.user_avatar as sender_avatar,
        IF((u.user_avatar IS NOT NULL AND u.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar)),
           '') AS sender_avatar_url
      FROM mo_support_ticket_messages tm
      LEFT JOIN nv_users u ON tm.created_by = u.id
      WHERE tm.ticket_id = :ticketId
        AND tm.message_type = 'TICKET_COMMENT'
        ${includePrivate ? "" : "AND tm.is_private = false"}
      ORDER BY tm.created_at DESC
    `;

    const comments = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: { ticketId },
    });

    return comments;
  } catch (error) {
    console.error("Error in getTicketCommentsRaw:", error);
    return [];
  }
};

/**
 * Get ticket messages with sender names using raw query
 * @param ticketId - Ticket ID
 * @param options - Query options
 * @returns Messages with sender information
 */
export const getTicketMessagesRaw = async (
  ticketId: number,
  options: {
    includePrivate?: boolean;
    canViewInternal?: boolean;
    messageType?: string;
    limit?: number;
    offset?: number;
  } = {}
): Promise<{ messages: any[]; total: number }> => {
  try {
    const {
      includePrivate = false,
      canViewInternal = false,
      messageType,
      limit,
      offset,
    } = options;

    // Build WHERE conditions
    const whereConditions = [`tm.ticket_id = :ticketId`];

    // Handle private message filtering
    if (!includePrivate) {
      whereConditions.push(`tm.is_private = false`);
    }

    // Handle message type filtering based on permissions
    if (!canViewInternal) {
      // Non-agents cannot see internal notes
      whereConditions.push(`tm.message_type NOT IN ('INTERNAL_NOTE')`);
      // Non-agents can NEVER see private messages (override includePrivate)
      whereConditions.push(`tm.is_private = false`);
    }

    // Specific message type filter
    if (messageType) {
      whereConditions.push(`tm.message_type = :messageType`);
    }

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM mo_support_ticket_messages tm
      WHERE ${whereConditions.join(" AND ")}
    `;

    // Main query with sender information
    const baseUrl = global.config?.API_BASE_URL || "";

    const messagesQuery = `
      SELECT
        tm.id,
        tm.ticket_id,
        tm.message_text,
        tm.message_type,
        tm.is_private,
        tm.attachment_id,
        tm.created_by,
        tm.created_at,
        tm.updated_at,
        -- Add sender full name, email and avatar
        CONCAT(u.user_first_name, ' ', u.user_last_name) as sender_name,
        u.user_email as sender_email,
        u.user_avatar as sender_avatar,
        IF((u.user_avatar IS NOT NULL AND u.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar)),
           '') AS sender_avatar_url
      FROM mo_support_ticket_messages tm
      LEFT JOIN nv_users u ON tm.created_by = u.id
      WHERE ${whereConditions.join(" AND ")}
      ORDER BY tm.created_at DESC
      ${limit ? `LIMIT ${limit}` : ""}
      ${offset ? `OFFSET ${offset}` : ""}
    `;

    const replacements: any = { ticketId };
    if (messageType) {
      replacements.messageType = messageType;
    }

    // Execute both queries
    const [countResult, messagesResult] = await Promise.all([
      sequelize.query(countQuery, {
        type: QueryTypes.SELECT,
        replacements,
      }),
      sequelize.query(messagesQuery, {
        type: QueryTypes.SELECT,
        replacements,
      }),
    ]);

    const total = (countResult[0] as any)?.total || 0;

    return {
      messages: messagesResult,
      total: parseInt(total),
    };
  } catch (error) {
    console.error("Error in getTicketMessagesRaw:", error);
    return { messages: [], total: 0 };
  }
};

/**
 * Calculate SLA due date based on ticket priority and support configuration.
 *
 * Order of precedence for SLA (in hours):
 *   1. Priority-specific config (sla_[priority]_priority_hours)
 *   2. Organization-level config (sla_resolution_time_hours)
 *   3. Fallback mapping by priority (hard-coded)
 *   4. Global default (72h)
 */
export const calculateSlaDueDate = (
  createdAt: Date,
  priority: string,
  orgConfig?: {
    sla_enabled?: boolean;
    sla_resolution_time_hours?: number;
    sla_low_priority_hours?: number;
    sla_medium_priority_hours?: number;
    sla_high_priority_hours?: number;
    sla_urgent_priority_hours?: number;
  }
): Date | null => {
  // Check if SLA is enabled for this organization
  if (orgConfig && orgConfig.sla_enabled === false) {
    return null; // SLA disabled
  }
  const fallbackByPriority: Record<string, number> = {
    [TICKET_PRIORITY.LOW]: 120, // 5 days
    [TICKET_PRIORITY.MEDIUM]: 72, // 3 days
    [TICKET_PRIORITY.HIGH]: 48, // 2 days
    [TICKET_PRIORITY.URGENT]: 24, // 1 day
  };

  let hours = 72; // global default

  // 1. Check for priority-specific configuration first
  if (orgConfig && priority) {
    switch (priority) {
      case TICKET_PRIORITY.LOW:
        if (orgConfig.sla_low_priority_hours) {
          hours = orgConfig.sla_low_priority_hours;
          break;
        }
        break;
      case TICKET_PRIORITY.MEDIUM:
        if (orgConfig.sla_medium_priority_hours) {
          hours = orgConfig.sla_medium_priority_hours;
          break;
        }
        break;
      case TICKET_PRIORITY.HIGH:
        if (orgConfig.sla_high_priority_hours) {
          hours = orgConfig.sla_high_priority_hours;
          break;
        }
        break;
      case TICKET_PRIORITY.URGENT:
        if (orgConfig.sla_urgent_priority_hours) {
          hours = orgConfig.sla_urgent_priority_hours;
          break;
        }
        break;
    }
  }

  // 2. Fallback to general organization config
  if (hours === 72 && orgConfig?.sla_resolution_time_hours) {
    hours = orgConfig.sla_resolution_time_hours;
  }

  // 3. Fallback to hard-coded priority mapping
  if (hours === 72 && priority && fallbackByPriority[priority]) {
    hours = fallbackByPriority[priority];
  }

  const dueDate = new Date(createdAt);
  dueDate.setHours(dueDate.getHours() + hours);
  return dueDate;
};
