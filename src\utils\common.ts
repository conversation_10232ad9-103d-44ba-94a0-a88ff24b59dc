import { QueryTypes } from "sequelize";
import { sequelize } from "../models";

// Global config declaration
// Global config access is handled through types/global.d.ts

/**
 * Get pagination parameters (NO DEFAULT VALUES)
 * Returns undefined if page or size not provided
 */
export const getPagination = (
  page?: string | number,
  size?: string | number
) => {
  // Handle empty strings and null/undefined values
  const cleanPage = page && page.toString().trim() !== '' ? page : undefined;
  const cleanSize = size && size.toString().trim() !== '' ? size : undefined;

  if (!cleanPage && !cleanSize) {
    return { limit: undefined, offset: undefined };
  }

  const limit = cleanSize ? +cleanSize : undefined;
  const offset = cleanPage && limit ? (+cleanPage - 1) * limit : undefined;
  return { limit, offset };
};

/**
 * Get paginated response structure (ENHANCED & ROBUST)
 * Standardized pagination format across all controllers
 */
export const getPaginatedItems = (
  data: any,
  page?: string | number,
  limit?: number
) => {
  // Handle both array data and Sequelize result format
  let items, totalItems;

  if (Array.isArray(data)) {
    items = data;
    totalItems = data.length;
  } else if (
    data &&
    typeof data === "object" &&
    "count" in data &&
    "rows" in data
  ) {
    totalItems = data.count;
    items = data.rows;
  } else {
    items = data || [];
    totalItems = items.length;
  }

  // If no pagination parameters, return simple structure
  if (!page || !limit) {
    return {
      items,
      totalItems,
    };
  }

  const currentPage = +page;
  const totalPages = Math.ceil(totalItems / limit);

  return {
    data: items,
    pagination: {
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
      // Additional metadata for better UX
      startIndex: (currentPage - 1) * limit + 1,
      endIndex: Math.min(currentPage * limit, totalItems),
    },
  };
};

/**
 * Standardized pagination query builder (ROBUST HELPER)
 * Use this in all controllers for consistent pagination logic
 */
export const buildPaginatedQuery = (
  baseQuery: any,
  page?: string | number,
  limit?: string | number
) => {
  const pagination = getPagination(page, limit);
  const isPaginated = !!(page || limit);

  // Clone the base query to avoid mutations
  const query = { ...baseQuery };

  // Only add pagination if explicitly requested
  if (isPaginated && pagination.limit) {
    query.limit = pagination.limit;
    query.offset = pagination.offset;
  }

  return {
    query,
    isPaginated,
    pagination,
  };
};

/**
 * Format paginated response consistently (STANDARDIZED HELPER)
 * Use this in all controllers for consistent response format
 */
export const formatPaginatedResponse = (
  result: any,
  isPaginated: boolean,
  page?: string | number,
  limit?: number,
  additionalData?: any
) => {
  if (isPaginated) {
    const paginatedData = getPaginatedItems(result, page, limit);
    return additionalData
      ? { ...paginatedData, ...additionalData }
      : paginatedData;
  } else {
    const { count, rows } = result;
    const baseResponse = {
      [getDataKey(rows)]: rows,
      totalCount: count,
    };
    return additionalData
      ? { ...baseResponse, ...additionalData }
      : baseResponse;
  }
};

/**
 * Get appropriate data key based on content type (HELPER)
 */
const getDataKey = (data: any[]): string => {
  if (!data || data.length === 0) return "items";

  // Try to determine data type from first item
  const firstItem = data[0];
  if (firstItem.ticket_number) return "tickets";
  if (firstItem.message_text) return "messages";
  if (firstItem.organization_id && firstItem.support_pin) return "configs";
  if (firstItem.user_email) return "users";

  return "items"; // Default fallback
};

/**
 * Get user details by ID or keycloak_auth_id
 * Following the exact same pattern as recipe module
 */
export const getUser = async (id: any, isAuth: boolean = false) => {
  const findUser = await sequelize.query(
    `SELECT
      id,
      user_first_name,
      user_middle_name,
      user_last_name,
      user_email,
      user_phone_number,
      branch_id,
      department_id,
      IF((user_avatar IS NOT NULL AND user_avatar != ''),
         CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)),
         '') AS user_avatar_link,
      user_avatar,
      user_status,
      user_active_role_id,
      web_user_active_role_id,
      webAppToken,
      appToken,
      CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, '')) as user_full_name,
      rota_group_by,
      list_order,
      organization_id,
      keycloak_auth_id
    FROM nv_users
    WHERE ${isAuth ? `keycloak_auth_id='${id}'` : `id = ${id}`}
      AND user_status NOT IN ('cancelled', 'deleted')`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );

  return findUser && findUser.length > 0 ? findUser[0] : null;
};

/**
 * Get multiple users by IDs
 * Following the exact same pattern as recipe module
 */
export const getUsers = async (ids: any) => {
  const findUsers = await sequelize.query(
    `SELECT
      id,
      user_first_name,
      user_middle_name,
      user_last_name,
      user_email,
      user_phone_number,
      branch_id,
      department_id,
      IF((user_avatar IS NOT NULL AND user_avatar != ''),
         CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)),
         '') AS user_avatar_link,
      user_avatar,
      user_status,
      user_active_role_id,
      web_user_active_role_id,
      webAppToken,
      appToken,
      CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, '')) as user_full_name,
      rota_group_by,
      list_order,
      organization_id
    FROM nv_users
    WHERE id IN (${ids.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return findUsers;
};

/**
 * Get all roles for a user
 * Following the exact same pattern as recipe module
 */
export const getUserAllRoles = async (userId: number) => {
  const userRoles = await sequelize.query(
    `SELECT r.id, r.role_name FROM nv_user_roles ur INNER JOIN nv_roles r ON ur.role_id = r.id WHERE ur.user_id = ${userId}`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return userRoles;
};

/**
 * Get user session for token validation
 * Following the exact same pattern as recipe module
 */
export const getUserSession = async (token: string, deviceType: string) => {
  const userSession = await sequelize.query(
    `SELECT * FROM nv_user_session WHERE token = '${token}' AND device_type = '${deviceType}'`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return userSession && userSession.length > 0 ? userSession[0] : null;
};

/**
 * Get user full name by user ID
 * Following the exact same pattern as recipe module
 * @param user_id - User ID
 * @returns Promise<string> - User full name or fallback
 */
export const getUserFullName = async (user_id: any): Promise<string> => {
  try {
    if (!user_id) return "Unknown User";

    // Reuse existing getUser function to avoid code duplication
    const user = await getUser(user_id);

    if (!user) {
      return "Unknown User";
    }

    // Use the enhanced name construction logic
    return constructUserFullName(user) || user.user_email || "Unknown User";
  } catch (error) {
    console.error("Error in getUserFullName:", error);
    return "Unknown User";
  }
};

/**
 * Construct full name from user object (SHARED UTILITY)
 * Centralized name construction logic to avoid duplication
 */
export const constructUserFullName = (user: any): string => {
  if (!user) return "Unknown User";

  // Use pre-computed full name if available
  if (user.user_full_name && user.user_full_name.trim()) {
    return user.user_full_name.trim();
  }

  // Construct from individual parts
  const firstName = (user.user_first_name || "").trim();
  const middleName = (user.user_middle_name || "").trim();
  const lastName = (user.user_last_name || "").trim();

  const nameParts = [firstName, middleName, lastName].filter(
    (part) => part.length > 0
  );
  return nameParts.length > 0 ? nameParts.join(" ") : "Unknown User";
};

/**
 * Sanitize and extract user contact data (SHARED UTILITY)
 * Centralized contact data extraction to avoid duplication
 */
export const extractUserContactData = (user: any) => {
  if (!user) {
    return {
      email: "",
      phone: "",
      firstName: "",
      middleName: "",
      lastName: "",
      fullName: "Unknown User",
    };
  }

  const firstName = (user.user_first_name || "").trim();
  const middleName = (user.user_middle_name || "").trim();
  const lastName = (user.user_last_name || "").trim();
  const email = (user.user_email || "").trim();
  const phone = (user.user_phone_number || "").trim();
  const fullName = constructUserFullName(user);

  return {
    email,
    phone,
    firstName,
    middleName,
    lastName,
    fullName,
  };
};

/**
 * Get all assignable users for support tickets (agents, admins, support staff)
 * @param organizationId - Organization ID to filter users
 * @param search - Optional search term for user name/email
 * @param roleFilter - Optional role filter (admin, agent, support)
 * @param departmentId - Optional department filter
 * @param branchId - Optional branch filter
 * @param page - Page number for pagination
 * @param limit - Items per page
 */
export const getAssignableUsers = async (
  organizationId: string,
  options: {
    search?: string;
    roleFilter?: string;
    departmentId?: number;
    branchId?: number;
    page?: number;
    limit?: number;
    includeInactive?: boolean;
  } = {}
) => {
  try {
    const {
      search,
      roleFilter,
      departmentId,
      branchId,
      page,
      limit,
      includeInactive = false,
    } = options;

    // Build WHERE conditions
    const whereConditions = [
      `u.organization_id = '${organizationId}'`,
      includeInactive
        ? `u.user_status NOT IN ('cancelled', 'deleted')`
        : `u.user_status = 'active'`,
    ];

    // Add search filter
    if (search) {
      whereConditions.push(`(
        u.user_first_name LIKE '%${search}%' OR
        u.user_last_name LIKE '%${search}%' OR
        u.user_email LIKE '%${search}%' OR
        CONCAT(u.user_first_name, ' ', u.user_last_name) LIKE '%${search}%'
      )`);
    }

    // Add department filter
    if (departmentId) {
      whereConditions.push(`u.department_id = ${departmentId}`);
    }

    // Add branch filter
    if (branchId) {
      whereConditions.push(`u.branch_id = ${branchId}`);
    }

    // Role filter - get users with support-related roles
    let roleJoin = "";
    if (roleFilter) {
      roleJoin = `
        INNER JOIN nv_user_roles ur ON u.id = ur.user_id
        INNER JOIN nv_roles r ON ur.role_id = r.id
      `;

      switch (roleFilter.toLowerCase()) {
        case "admin":
          whereConditions.push(`r.role_name LIKE '%admin%'`);
          break;
        case "agent":
          whereConditions.push(
            `r.role_name LIKE '%agent%' OR r.role_name LIKE '%support%'`
          );
          break;
        case "support":
          whereConditions.push(`(
            r.role_name LIKE '%support%' OR
            r.role_name LIKE '%agent%' OR
            r.role_name LIKE '%admin%' OR
            r.role_name LIKE '%manager%'
          )`);
          break;
        default:
          whereConditions.push(`r.role_name LIKE '%${roleFilter}%'`);
      }
    } else {
      // Default: only get users with support-related roles
      roleJoin = `
        INNER JOIN nv_user_roles ur ON u.id = ur.user_id
        INNER JOIN nv_roles r ON ur.role_id = r.id
      `;
      whereConditions.push(`(
        r.role_name LIKE '%support%' OR
        r.role_name LIKE '%agent%' OR
        r.role_name LIKE '%admin%' OR
        r.role_name LIKE '%manager%'
      )`);
    }

    // Build the main query
    let query = `
      SELECT DISTINCT
        u.id,
        u.user_first_name,
        u.user_middle_name,
        u.user_last_name,
        u.user_email,
        u.branch_id,
        u.department_id,
        u.user_status,
        u.user_designation,
        u.user_phone_number,
        CONCAT(u.user_first_name, ' ', u.user_last_name) as user_full_name,
        IF((u.user_avatar IS NOT NULL AND u.user_avatar != ''),
           CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar)),
           '') AS user_avatar_link,
        b.branch_name,
        d.department_name,
        GROUP_CONCAT(DISTINCT r.role_name) as roles
      FROM nv_users u
      ${roleJoin}
      LEFT JOIN nv_branches b ON u.branch_id = b.id
      LEFT JOIN nv_departments d ON u.department_id = d.id
      WHERE ${whereConditions.join(" AND ")}
      GROUP BY u.id
      ORDER BY u.user_first_name ASC, u.user_last_name ASC
    `;

    // Only add pagination if both page and limit are provided
    if (page && limit) {
      const offset = (page - 1) * limit;
      query += ` LIMIT ${limit} OFFSET ${offset}`;
    }

    // Count query
    const countQuery = `
      SELECT COUNT(DISTINCT u.id) as total
      FROM nv_users u
      ${roleJoin}
      WHERE ${whereConditions.join(" AND ")}
    `;

    // Execute queries
    const [users, countResult] = await Promise.all([
      sequelize.query(query, { type: QueryTypes.SELECT, raw: true }),
      sequelize.query(countQuery, { type: QueryTypes.SELECT, raw: true }),
    ]);

    const total = (countResult[0] as any)?.total || 0;

    // Return different structure based on whether pagination was requested
    if (page && limit) {
      const totalPages = Math.ceil(total / limit);
      return {
        users,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      };
    } else {
      return {
        users,
        totalItems: total,
      };
    }
  } catch (error) {
    console.error("Error in getAssignableUsers:", error);
    throw error;
  }
};

/**
 * Get user details with roles for ticket assignment
 * @param userId - User ID
 * @param organizationId - Organization ID for security
 */
export const getUserForAssignment = async (
  userId: number,
  organizationId: string
) => {
  try {
    const query = `
      SELECT
        u.id,
        u.user_first_name,
        u.user_middle_name,
        u.user_last_name,
        u.user_email,
        u.user_status,
        u.user_designation,
        u.branch_id,
        u.department_id,
        u.organization_id,
        CONCAT(u.user_first_name, ' ', u.user_last_name) as user_full_name,
        b.branch_name,
        d.department_name,
        GROUP_CONCAT(DISTINCT r.role_name) as roles,
        COUNT(DISTINCT st.id) as active_tickets_count
      FROM nv_users u
      LEFT JOIN nv_branches b ON u.branch_id = b.id
      LEFT JOIN nv_departments d ON u.department_id = d.id
      LEFT JOIN nv_user_roles ur ON u.id = ur.user_id
      LEFT JOIN nv_roles r ON ur.role_id = r.id
      LEFT JOIN mo_support_tickets st ON u.id = st.assigned_to_user_id
        AND st.ticket_status IN ('open', 'in_progress', 'pending')
      WHERE u.id = ${userId}
        AND u.organization_id = '${organizationId}'
        AND u.user_status NOT IN ('cancelled', 'deleted')
      GROUP BY u.id
    `;

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      raw: true,
    });

    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error("Error in getUserForAssignment:", error);
    throw error;
  }
};

/**
 * Get user workload for assignment optimization
 * @param organizationId - Organization ID
 * @param userIds - Optional array of user IDs to check
 */
export const getUserWorkloads = async (
  organizationId: string,
  userIds?: number[]
) => {
  try {
    let userFilter = "";
    if (userIds && userIds.length > 0) {
      userFilter = `AND u.id IN (${userIds.join(",")})`;
    }

    const query = `
      SELECT
        u.id,
        u.user_first_name,
        u.user_last_name,
        CONCAT(u.user_first_name, ' ', u.user_last_name) as user_full_name,
        COUNT(DISTINCT CASE WHEN st.ticket_status = 'open' THEN st.id END) as open_tickets,
        COUNT(DISTINCT CASE WHEN st.ticket_status = 'in_progress' THEN st.id END) as in_progress_tickets,
        COUNT(DISTINCT CASE WHEN st.ticket_status = 'pending' THEN st.id END) as pending_tickets,
        COUNT(DISTINCT CASE WHEN st.ticket_status IN ('open', 'in_progress', 'pending') THEN st.id END) as total_active_tickets,
        COUNT(DISTINCT CASE WHEN st.priority = 'critical' AND st.ticket_status IN ('open', 'in_progress') THEN st.id END) as critical_tickets,
        COUNT(DISTINCT CASE WHEN st.priority = 'high' AND st.ticket_status IN ('open', 'in_progress') THEN st.id END) as high_priority_tickets,
        AVG(CASE WHEN st.ticket_status = 'resolved' THEN
          TIMESTAMPDIFF(HOUR, st.created_at, st.resolved_at) END) as avg_resolution_time_hours
      FROM nv_users u
      INNER JOIN nv_user_roles ur ON u.id = ur.user_id
      INNER JOIN nv_roles r ON ur.role_id = r.id
      LEFT JOIN mo_support_tickets st ON u.id = st.assigned_to_user_id
      WHERE u.organization_id = '${organizationId}'
        AND u.user_status = 'active'
        AND (r.role_name LIKE '%support%' OR r.role_name LIKE '%agent%' OR r.role_name LIKE '%admin%')
        ${userFilter}
      GROUP BY u.id
      ORDER BY total_active_tickets ASC, critical_tickets ASC
    `;

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      raw: true,
    });

    return result;
  } catch (error) {
    console.error("Error in getUserWorkloads:", error);
    throw error;
  }
};

/**
 * Check if user has default/super admin access
 * Following the same pattern as recipe module
 */
export const isDefaultAccess = async (userId: any): Promise<boolean> => {
  try {
    // Input validation
    if (!userId) {
      console.log("isDefaultAccess: userId is required");
      return false;
    }

    // Get user from database
    const user = await getUser(userId, false);

    // If user not found, return false
    if (!user || !user.keycloak_auth_id) {
      console.log(`isDefaultAccess: User not found for ID: ${userId}`);
      return false;
    }

    // Get user roles from Keycloak using parameterized query to prevent SQL injection
    const userRoles = await sequelize.query(
      `SELECT ROLE_ID FROM USER_ROLE_MAPPING WHERE USER_ID = :userId`,
      {
        type: QueryTypes.SELECT,
        raw: true,
        replacements: { userId: user.keycloak_auth_id },
      }
    );

    // If no roles found, return false
    if (!userRoles || userRoles.length === 0) {
      console.log(
        `isDefaultAccess: No roles found for user: ${user.keycloak_auth_id}`
      );
      return false;
    }

    // Extract role IDs
    const roleIds = userRoles.map((role: any) => role.ROLE_ID);

    // Get role details using parameterized query
    const roles = await sequelize.query(
      `SELECT NAME, DESCRIPTION FROM KEYCLOAK_ROLE WHERE ID IN (:roleIds)`,
      {
        type: QueryTypes.SELECT,
        raw: true,
        replacements: { roleIds },
      }
    );

    // Check if user has default creation role
    const hasDefaultRole = roles.some(
      (role: any) =>
        role.NAME === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
        role.DESCRIPTION === global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION
    );

    return hasDefaultRole;
  } catch (error) {
    // Log error in development mode only
    if (process.env.NODE_ENV === "development") {
      console.error("isDefaultAccess Exception:", error);
    }
    return false;
  }
};

/**
 * Check if user is an agent for the support ticket system
 * Agents are users from the organization specified in ORGANIZATION_ID config
 * These users can create and view internal notes, and can be assigned tickets
 * @param userId - User ID to check
 * @returns Promise<boolean> - true if user is an agent
 */
export const isAgent = async (userId: any): Promise<boolean> => {
  try {
    // Input validation
    if (!userId) {
      console.log("isAgent: userId is required");
      return false;
    }

    // Super admins are always considered agents
    if (await isDefaultAccess(userId)) {
      return true;
    }

    // Get organization ID from config
    const organizationId = global.config?.ORGANIZATION_ID;
    if (!organizationId) {
      console.log("isAgent: ORGANIZATION_ID not configured in config.json");
      return false;
    }

    // Get user from database
    const user = await getUser(userId, false);
    if (!user) {
      console.log(`isAgent: User not found for ID: ${userId}`);
      return false;
    }

    // Check if user belongs to the agent organization
    // All users from this organization are considered agents
    const isFromAgentOrg = user.organization_id === organizationId;

    console.log(
      `isAgent: User ${userId} - organization match: ${isFromAgentOrg} (user org: ${user.organization_id}, agent org: ${organizationId})`
    );
    return isFromAgentOrg;
  } catch (error) {
    console.error("isAgent Exception:", error);
    return false;
  }
};

/**
 * Check if user is a regular agent (not super admin)
 * This is used for validation where super admins should have different rules
 * @param userId - User ID to check
 * @returns Promise<boolean> - true if user is a regular agent (not super admin)
 */
export const isRegularAgent = async (userId: any): Promise<boolean> => {
  try {
    // Input validation
    if (!userId) {
      return false;
    }

    // Super admins are NOT regular agents
    if (await isDefaultAccess(userId)) {
      return false;
    }

    // Check if user is from agent organization
    return await isAgent(userId);
  } catch (error) {
    console.error("isRegularAgent Exception:", error);
    return false;
  }
};

/**
 * Check if user has full ticket access (can access all tickets across organizations)
 * This includes:
 * 1. Super admin users (isDefaultAccess)
 * 2. Users from the agent organization (ORGANIZATION_ID config)
 * @param userId - User ID to check
 * @returns Promise<boolean> - true if user has full ticket access
 */
export const hasFullTicketAccess = async (userId: any): Promise<boolean> => {
  try {
    // Input validation
    if (!userId) {
      return false;
    }

    // Check if user is super admin
    if (await isDefaultAccess(userId)) {
      return true;
    }

    // Check if user is from agent organization
    if (await isAgent(userId)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error("hasFullTicketAccess Exception:", error);
    return false;
  }
};

/**
 * Check if user can view internal notes
 * Only agents (users from ORGANIZATION_ID config) and super admins can view internal notes
 * @param userId - User ID to check
 * @returns Promise<boolean> - true if user can view internal notes
 */
export const canViewInternalNotes = async (userId: any): Promise<boolean> => {
  return await isAgent(userId);
};

/**
 * Check if user can create internal notes
 * Only agents (users from ORGANIZATION_ID config) and super admins can create internal notes
 * @param userId - User ID to check
 * @returns Promise<boolean> - true if user can create internal notes
 */
export const canCreateInternalNotes = async (userId: any): Promise<boolean> => {
  return await isAgent(userId);
};

/**
 * Check if user has permission for specific action
 */
export const hasPermission = async (
  userId: number,
  _module: string,
  _action: string
): Promise<boolean> => {
  try {
    // Get user from database
    const user = await getUser(userId);
    if (!user) return false;

    // Super admin has all permissions
    if (await isDefaultAccess(userId)) {
      return true;
    }

    // Add your permission checking logic here
    // This would typically check against a permissions table
    // For now, return true for basic functionality
    return true;
  } catch (error) {
    console.error("Permission check error:", error);
    return false;
  }
};

/**
 * Validate if user exists and is active
 * Used to verify user_id before storing in tickets
 */
export const validateUserExists = async (userId: number): Promise<boolean> => {
  try {
    if (!userId || typeof userId !== "number" || userId <= 0) {
      return false;
    }

    const user = await getUser(userId);
    return (
      user &&
      user.id &&
      user.user_status &&
      !["cancelled", "deleted", "inactive"].includes(
        user.user_status.toLowerCase()
      )
    );
  } catch (error) {
    console.error("Error validating user existence:", error);
    return false;
  }
};

/**
 * Resolve contact information for ticket creation/update
 * Priority: Manual input > nv_users data
 * @param userId - User ID from authentication
 * @param manualName - Manually provided name (optional)
 * @param manualEmail - Manually provided email (optional)
 * @param manualPhone - Manually provided phone (optional)
 * @returns Object with resolved contact information
 */
export const resolveContactInformation = async (
  userId: number,
  manualName?: string,
  manualEmail?: string,
  manualPhone?: string
) => {
  try {
    // Get user details from nv_users
    const userDetails = await getUser(userId);

    if (!userDetails) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Build full name from nv_users
    const userFullName =
      `${userDetails.user_first_name || ""} ${userDetails.user_last_name || ""}`.trim();

    // Return resolved contact information with priority: manual > nv_users
    return {
      // Store manual overrides if provided, otherwise null (will fetch from nv_users when needed)
      ticket_owner_name: manualName?.trim() || null,
      ticket_owner_email: manualEmail?.trim() || null,
      ticket_owner_phone: manualPhone?.trim() || null,

      // For immediate use (response/notifications)
      resolved_name:
        manualName?.trim() || userFullName || userDetails.user_email,
      resolved_email: manualEmail?.trim() || userDetails.user_email,
      resolved_phone:
        manualPhone?.trim() || userDetails.user_phone_number || "",

      // User details for reference
      user_details: userDetails,
    };
  } catch (error) {
    console.error("Error resolving contact information:", error);
    throw new Error("Failed to resolve user contact information");
  }
};

/**
 * Get ticket owner details with avatar support
 * This function prioritizes user_id over fallback name/email for registered users
 * Enables future avatar display and profile features
 */
export const getTicketOwnerDetails = async (ticket: any) => {
  try {
    // Input validation
    if (!ticket) {
      throw new Error("Ticket object is required");
    }

    // If ticket has a registered user ID, fetch full user details (including avatar and phone)
    if (
      ticket.ticket_owner_user_id &&
      typeof ticket.ticket_owner_user_id === "number" &&
      ticket.ticket_owner_user_id > 0
    ) {
      const userDetails = await getUser(ticket.ticket_owner_user_id);
      if (userDetails && userDetails.id) {
        // Use shared contact data extraction utility to avoid duplication
        const contactData = extractUserContactData(userDetails);

        // Priority: Manual overrides (ticket fields) > nv_users data
        const resolvedName = ticket.ticket_owner_name || contactData.fullName;
        const resolvedEmail = ticket.ticket_owner_email || contactData.email;
        const resolvedPhone = ticket.ticket_owner_phone || contactData.phone;

        return {
          user_id: userDetails.id,
          name: resolvedName,
          email: resolvedEmail,
          phone: resolvedPhone,
          avatar_url: userDetails.user_avatar_link || null,
          is_registered_user: true,
          // Additional user profile data for enhanced features
          first_name: contactData.firstName,
          middle_name: contactData.middleName,
          last_name: contactData.lastName,
          organization_id: userDetails.organization_id || "",
          branch_id: userDetails.branch_id || null,
          department_id: userDetails.department_id || null,
          // Source information for debugging
          source_name: ticket.ticket_owner_name ? "manual" : "nv_users",
          source_email: ticket.ticket_owner_email ? "manual" : "nv_users",
          source_phone: ticket.ticket_owner_phone ? "manual" : "nv_users",
          // Original nv_users data for reference
          nv_users_name: contactData.fullName,
          nv_users_email: contactData.email,
          nv_users_phone: contactData.phone,
        };
      }
    }

    // Fallback to ticket stored data (for guest users or if user not found)
    const fallbackName =
      (ticket.ticket_owner_name || "").trim() || "Unknown User";
    const fallbackEmail = (ticket.ticket_owner_email || "").trim();
    const fallbackPhone = (ticket.ticket_owner_phone || "").trim();

    return {
      user_id: null,
      name: fallbackName,
      email: fallbackEmail,
      phone: fallbackPhone,
      avatar_url: null,
      is_registered_user: false,
      first_name: "",
      middle_name: "",
      last_name: "",
      organization_id: "",
      branch_id: null,
      department_id: null,
      fallback_name: fallbackName,
      fallback_email: fallbackEmail,
      fallback_phone: fallbackPhone,
    };
  } catch (error) {
    console.error("Error getting ticket owner details:", error);
    // Return safe fallback data on error with proper null checks
    const safeName = ticket?.ticket_owner_name?.trim() || "Unknown User";
    const safeEmail = ticket?.ticket_owner_email?.trim() || "";
    const safePhone = ticket?.ticket_owner_phone?.trim() || "";

    return {
      user_id: null,
      name: safeName,
      email: safeEmail,
      phone: safePhone,
      avatar_url: null,
      is_registered_user: false,
      first_name: "",
      middle_name: "",
      last_name: "",
      organization_id: "",
      branch_id: null,
      department_id: null,
      fallback_name: safeName,
      fallback_email: safeEmail,
      fallback_phone: safePhone,
    };
  }
};

/**
 * Get follower details with user information
 * @param followerIds Array of user IDs
 * @returns Array of follower objects with user details
 */
export const getFollowersDetails = async (
  followerIds: number[]
): Promise<any[]> => {
  if (!followerIds || !Array.isArray(followerIds) || followerIds.length === 0) {
    return [];
  }

  try {
    const baseUrl = global.config?.API_BASE_URL || "";

    // First, let's check which users exist and their status
    const checkQuery = `
      SELECT
        u.id,
        u.user_first_name,
        u.user_last_name,
        u.user_email,
        u.user_status,
        u.user_avatar
      FROM nv_users u
      WHERE u.id IN (${followerIds.map(() => '?').join(',')})
    `;

    const allUsers = await sequelize.query(checkQuery, {
      type: QueryTypes.SELECT,
      replacements: followerIds,
    });

    // Main query - get active users with proper avatar URLs
    const query = `
      SELECT
        u.id,
        CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, '')) AS full_name,
        u.user_email as email,
        IF((u.user_avatar IS NOT NULL AND u.user_avatar != ''),
           CONCAT('${baseUrl}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar)),
           '') AS avatar
      FROM nv_users u
      WHERE u.id IN (${followerIds.map(() => '?').join(',')})
        AND u.user_status IN ('active', 'verified', 'ongoing', 'pending')
      ORDER BY u.user_first_name ASC, u.user_last_name ASC
    `;

    const followers = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: followerIds,
    });

    // Create result array, including inactive users with basic info
    const result = [];

    for (const id of followerIds) {
      const activeFollower = followers.find((f: any) => f.id === id);

      if (activeFollower) {
        // Active user found
        result.push({
          id: activeFollower.id,
          full_name: activeFollower.full_name?.trim() || activeFollower.email || `User ${activeFollower.id}`,
          email: activeFollower.email || '',
          avatar: activeFollower.avatar || '',
        });
      } else {
        // Check if user exists but is inactive
        const inactiveUser = allUsers.find((u: any) => u.id === id);
        if (inactiveUser) {
          const fullName = `${inactiveUser.user_first_name || ''} ${inactiveUser.user_last_name || ''}`.trim();
          result.push({
            id: inactiveUser.id,
            full_name: fullName || inactiveUser.user_email || `User ${inactiveUser.id}`,
            email: inactiveUser.user_email || '',
            avatar: '', // Don't show avatar for inactive users
          });
        } else {
          // User doesn't exist at all
          result.push({
            id,
            full_name: `User ${id}`,
            email: '',
            avatar: '',
          });
        }
      }
    }

    return result;
  } catch (error) {
    console.error("Error getting followers details:", error);
    // Return fallback data for users that couldn't be found
    return followerIds.map(id => ({
      id,
      full_name: `User ${id}`,
      email: '',
      avatar: '',
    }));
  }
};

/**
 * Generate unique ticket number
 */
export const generateTicketNumber = async (
  organizationId?: string
): Promise<string> => {
  try {
    const orgPrefix = organizationId
      ? organizationId.substring(0, 3).toUpperCase()
      : "ORG";

    // Get count of tickets for this organization
    const countResult = await sequelize.query(
      `SELECT COUNT(*) as count FROM mo_support_tickets WHERE organization_id = :orgId`,
      {
        type: QueryTypes.SELECT,
        raw: true,
        replacements: { orgId: organizationId || "default" },
      }
    );

    const count = (countResult[0] as any)?.count || 0;
    const sequence = (count + 1).toString().padStart(4, "0");

    return `${orgPrefix}-TKT-${sequence}`;
  } catch (error) {
    console.error("Error generating ticket number:", error);
    return `TKT-${Date.now()}`;
  }
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Sanitize input to prevent XSS
 */
export const sanitizeInput = (input: string | null | undefined): string => {
  if (typeof input !== "string" || input === null || input === undefined)
    return "";

  return input
    .replace(/[<>]/g, "") // Remove < and >
    .replace(/javascript:/gi, "") // Remove javascript: protocol
    .replace(/on\w+=/gi, "") // Remove event handlers
    .trim();
};

/**
 * Calculate SLA due date based on priority
 * @deprecated Use calculateSlaDueDate from ticket.helper.ts instead
 * This function is kept for backward compatibility but should not be used for new code
 */
export const calculateSLADueDate = (
  priority: string,
  createdAt: Date = new Date(),
  orgConfig?: { sla_enabled?: boolean }
): Date | null => {
  // Check if SLA is disabled
  if (orgConfig && orgConfig.sla_enabled === false) {
    return null;
  }

  const slaHours: any = {
    low: 24,
    medium: 8,
    high: 4,
    urgent: 2,
    emergency: 1,
  };

  const hours = slaHours[priority.toLowerCase()] || slaHours.medium;
  const dueDate = new Date(createdAt);
  dueDate.setHours(dueDate.getHours() + hours);

  return dueDate;
};

/**
 * Format date for display
 */
export const formatDate = (date: Date | string | null | undefined): string => {
  if (!date) return "Invalid Date";

  try {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) return "Invalid Date";

    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(dateObj);
  } catch {
    return "Invalid Date";
  }
};

/**
 * Get time ago string
 */
export const getTimeAgo = (date: Date | string | null | undefined): string => {
  if (!date) return "Invalid Date";

  try {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) return "Invalid Date";

    const now = new Date();
    const diffInSeconds = Math.floor(
      (now.getTime() - dateObj.getTime()) / 1000
    );

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000)
      return `${Math.floor(diffInSeconds / 86400)} days ago`;

    return formatDate(dateObj);
  } catch {
    return "Invalid Date";
  }
};

/**
 * Support Ticket File Upload Constants ()
 */
export const SUPPORT_FILE_UPLOAD_CONSTANT = Object.freeze({
  TICKET_ATTACHMENT: {
    folder: "support_attachments",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_attachments/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_attachments/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  MESSAGE_ATTACHMENT: {
    folder: "message_attachments",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      messageId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/message_attachments/${ticketId}/${messageId ? `${messageId}/` : ""}${fileName}`
        : `support_defaults/message_attachments/${ticketId}/${messageId ? `${messageId}/` : ""}${fileName}`,
  },
});

/**
 * Legacy constants for backward compatibility
 */
export const TICKET_FILE_UPLOAD_CONSTANT = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "application/pdf",
    "text/plain",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ],
  UPLOAD_PATH: "uploads/tickets/",
};

/**
 * Get tickets where user is a follower
 * @param userId - User ID to check for follower access
 * @returns Array of ticket IDs where user is a follower
 */
export const getFollowedTicketIds = async (userId: number): Promise<number[]> => {
  if (!userId) {
    return [];
  }

  try {
    const query = `
      SELECT id
      FROM mo_support_tickets
      WHERE JSON_CONTAINS(followers, :userIdJson)
        AND deleted_at IS NULL
    `;

    const tickets = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        userIdJson: JSON.stringify(userId),
      },
    });

    return tickets.map((ticket: any) => ticket.id);
  } catch (error) {
    console.error("Error getting followed ticket IDs:", error);
    return [];
  }
};

/**
 * Build follower access condition for SQL queries
 * @param userId - User ID to check for follower access
 * @returns SQL condition string for follower access
 */
export const buildFollowerAccessCondition = (userId: number): string => {
  if (!userId) {
    return "";
  }
  return `JSON_CONTAINS(t.followers, '${userId}')`;
};

export default {
  // ✅ ENHANCED PAGINATION UTILITIES
  getPagination,
  getPaginatedItems,
  buildPaginatedQuery,
  formatPaginatedResponse,
  // User management utilities
  getUser,
  getUsers,
  getUserFullName,
  constructUserFullName,
  extractUserContactData,
  getUserAllRoles,
  getUserSession,
  getAssignableUsers,
  getUserForAssignment,
  getUserWorkloads,
  validateUserExists,
  getTicketOwnerDetails,
  // Permission and access utilities
  isDefaultAccess,
  isAgent,
  isRegularAgent,
  hasFullTicketAccess,
  canViewInternalNotes,
  canCreateInternalNotes,
  hasPermission,
  // General utilities
  generateTicketNumber,
  isValidEmail,
  sanitizeInput,
  calculateSLADueDate,
  formatDate,
  getTimeAgo,
  // Follower utilities
  getFollowersDetails,
  getFollowedTicketIds,
  buildFollowerAccessCondition,
  // Constants
  SUPPORT_FILE_UPLOAD_CONSTANT,
  TICKET_FILE_UPLOAD_CONSTANT,
};
