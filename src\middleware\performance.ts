import { Request, Response, NextFunction } from 'express';

/**
 * Performance monitoring middleware
 * Tracks API response times and logs slow requests
 */
export const performanceMonitor = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const originalSend = res.send;

  // Override res.send to capture response time
  res.send = function(body: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Add performance headers
    res.set('X-Response-Time', `${duration}ms`);
    
    // Log slow requests (over 2 seconds)
    if (duration > 2000) {
      console.warn(`🐌 SLOW API: ${req.method} ${req.originalUrl} - ${duration}ms`);
      console.warn(`   Query params:`, req.query);
      console.warn(`   Body size:`, JSON.stringify(req.body).length, 'chars');
    } else if (duration > 1000) {
      console.log(`⏱️  API: ${req.method} ${req.originalUrl} - ${duration}ms`);
    }
    
    // Call original send
    return originalSend.call(this, body);
  };

  next();
};

/**
 * Database query performance tracker
 */
export class QueryPerformanceTracker {
  private static slowQueries: Array<{
    query: string;
    duration: number;
    timestamp: Date;
    params?: any;
  }> = [];

  static trackQuery(query: string, duration: number, params?: any) {
    if (duration > 1000) { // Track queries over 1 second
      this.slowQueries.push({
        query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
        duration,
        timestamp: new Date(),
        params
      });

      // Keep only last 50 slow queries
      if (this.slowQueries.length > 50) {
        this.slowQueries = this.slowQueries.slice(-50);
      }

      console.warn(`🐌 SLOW QUERY: ${duration}ms - ${query.substring(0, 100)}...`);
    }
  }

  static getSlowQueries() {
    return this.slowQueries;
  }

  static clearSlowQueries() {
    this.slowQueries = [];
  }
}

/**
 * Memory usage monitoring
 */
export const logMemoryUsage = () => {
  const used = process.memoryUsage();
  const usage = {
    rss: Math.round(used.rss / 1024 / 1024 * 100) / 100,
    heapTotal: Math.round(used.heapTotal / 1024 / 1024 * 100) / 100,
    heapUsed: Math.round(used.heapUsed / 1024 / 1024 * 100) / 100,
    external: Math.round(used.external / 1024 / 1024 * 100) / 100
  };

  if (usage.heapUsed > 500) { // Log if heap usage over 500MB
    console.warn(`🧠 HIGH MEMORY USAGE:`, usage, 'MB');
  }

  return usage;
};

/**
 * Request size monitoring
 */
export const requestSizeMonitor = (req: Request, res: Response, next: NextFunction) => {
  const bodySize = JSON.stringify(req.body || {}).length;
  const querySize = JSON.stringify(req.query || {}).length;
  
  if (bodySize > 100000) { // 100KB
    console.warn(`📦 LARGE REQUEST BODY: ${req.method} ${req.originalUrl} - ${bodySize} bytes`);
  }
  
  if (querySize > 10000) { // 10KB
    console.warn(`📦 LARGE QUERY STRING: ${req.method} ${req.originalUrl} - ${querySize} bytes`);
  }
  
  next();
};

export default {
  performanceMonitor,
  QueryPerformanceTracker,
  logMemoryUsage,
  requestSizeMonitor
};
